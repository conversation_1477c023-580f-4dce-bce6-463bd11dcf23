package com.wishforthecure.forconversations.hexa.domain.model.message;

import java.time.Instant;
import java.util.List;

import org.jmolecules.ddd.annotation.ValueObject;

import com.wishforthecure.forconversations.hexa.domain.model.alias.EmailRecipientAliasDomain;
import com.wishforthecure.forconversations.hexa.domain.model.alias.EmailSenderAliasDomain;
import com.wishforthecure.forconversations.hexa.domain.model.feeling.FeelingDomain;
import com.wishforthecure.forconversations.hexa.domain.model.source.SourceType;
import com.wishforthecure.forconversations.hexa.domain.model.tag.TagDomain;

import lombok.AllArgsConstructor;
import lombok.Value;

@ValueObject
@Value
@AllArgsConstructor
public class EmailMessageDomain implements Message {

    MessageId messageId;
    Instant timestamp;
    EmailSenderAliasDomain sender;
    List<EmailRecipientAliasDomain> recipients;
    String content;
    List<FeelingDomain> feelingDomainList;
    List<TagDomain> tagDomainList;
    SourceType sourceType;

    @Override
    public MessageId getMessageId() {
        return messageId;
    }

    @Override
    public Instant getTime() {
        return timestamp;
    }

    @Override
    public String getSender() {
        return sender.getValue();
    }

    @Override
    public String getRecipients() {
        return recipients.stream()
                .map(EmailRecipientAliasDomain::getValue)
                .collect(java.util.stream.Collectors.joining(", "));
    }

    @Override
    public String getContent() {
        return content;
    }

    @Override
    public List<FeelingDomain> getFeelingDomainList() {
        return feelingDomainList;
    }

    @Override
    public List<TagDomain> getTagDomainList() {
        return tagDomainList;
    }
}
