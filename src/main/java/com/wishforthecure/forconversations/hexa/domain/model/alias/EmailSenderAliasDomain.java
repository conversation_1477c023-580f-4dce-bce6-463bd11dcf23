package com.wishforthecure.forconversations.hexa.domain.model.alias;

import org.jmolecules.ddd.annotation.ValueObject;

import com.wishforthecure.forconversations.domain.enumeration.AliasType;

import lombok.AllArgsConstructor;
import lombok.Getter;

@ValueObject
@Getter
@AllArgsConstructor
public class EmailSenderAliasDomain implements Alias {

    private final String recipientName;
    private final String recipientEmail;

    @Override
    public String getName() {
        return recipientName;
    }

    @Override
    public String getValue() {
        return recipientEmail;
    }

    @Override
    public AliasType getAliasType() {
        return AliasType.EMAIL;
    }
}
