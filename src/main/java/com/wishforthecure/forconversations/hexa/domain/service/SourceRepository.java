package com.wishforthecure.forconversations.hexa.domain.service;

import java.util.Optional;

import org.jmolecules.ddd.annotation.Repository;

import com.wishforthecure.forconversations.hexa.domain.model.source.Source;
import com.wishforthecure.forconversations.hexa.domain.model.source.SourceId;

@Repository
public interface SourceRepository {
    Source save(Source source);

    Optional<Source> findById(SourceId id);
}
