package com.wishforthecure.forconversations.hexa.domain.model.filter;

import java.util.Set;

import org.jmolecules.ddd.annotation.ValueObject;

import com.wishforthecure.forconversations.hexa.domain.model.message.Message;

import lombok.Value;

@ValueObject
@Value
public class AddressFilter implements MessageFilter {

    Set<EmailAddress> addresses;
    FilterMode mode;

    public static AddressFilter of(Set<EmailAddress> addresses, FilterMode mode) {
        return new AddressFilter(addresses, mode);
    }

    @Override
    public boolean apply(Message message) {
        throw new UnsupportedOperationException("Unimplemented method 'apply'");
    }
}
