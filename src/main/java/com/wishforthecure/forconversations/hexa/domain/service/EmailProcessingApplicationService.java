package com.wishforthecure.forconversations.hexa.domain.service;

import java.time.Instant;
import java.util.List;
import java.util.Set;

import com.wishforthecure.forconversations.hexa.domain.model.filter.AddressFilter;
import com.wishforthecure.forconversations.hexa.domain.model.filter.DateRangeFilter;
import com.wishforthecure.forconversations.hexa.domain.model.filter.EmailAddress;
import com.wishforthecure.forconversations.hexa.domain.model.filter.FilterMode;
import com.wishforthecure.forconversations.hexa.domain.model.filter.KeywordFilter;
import com.wishforthecure.forconversations.hexa.domain.model.filter.MessageFilter;
import com.wishforthecure.forconversations.hexa.domain.model.message.EmailMessageDomain;
import com.wishforthecure.forconversations.hexa.domain.model.message.Message;

public class EmailProcessingApplicationService {

    private final MessageFilteringService filteringService;

    public EmailProcessingApplicationService(MessageFilteringService filteringService) {
        this.filteringService = filteringService;
    }

    public List<Message> processAndFilterEmails(List<EmailMessageDomain> incomingEmails,
            Instant startDate, Instant endDate,
            Set<String> keywords,
            Set<EmailAddress> addresses) {
        MessageFilter dateFilter = DateRangeFilter.of(startDate, endDate, FilterMode.INCLUDE);

        MessageFilter keywordFilter = KeywordFilter.of(keywords, FilterMode.EXCLUDE);

        MessageFilter addressFilter = AddressFilter.of(addresses, FilterMode.EXCLUDE);

        List<MessageFilter> activeFilters = List.of(dateFilter, keywordFilter, addressFilter);

        return filteringService.filterMessages(incomingEmails, activeFilters);
    }
}