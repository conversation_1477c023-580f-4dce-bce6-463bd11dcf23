package com.wishforthecure.forconversations.hexa.domain.model.alias;

import org.jmolecules.ddd.annotation.ValueObject;

import com.wishforthecure.forconversations.domain.enumeration.AliasType;

import lombok.AllArgsConstructor;
import lombok.Getter;

@ValueObject
@Getter
@AllArgsConstructor
public class WhatsAppRecipientAliasDomain implements Alias {

    private final String recipientName;
    private final String recipientMobile;

    @Override
    public String getName() {
        return recipientName;
    }

    @Override
    public String getValue() {
        return recipientMobile;
    }

    @Override
    public AliasType getAliasType() {
        return AliasType.WHATSAPP;
    }
}
