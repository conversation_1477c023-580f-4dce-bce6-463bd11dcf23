package com.wishforthecure.forconversations.hexa.domain.model.tag;

import lombok.Value;
import org.jmolecules.ddd.annotation.ValueObject;

@ValueObject
@Value
public final class TagDomain {

    String value;

    private TagDomain(String value) {
        this.value = value;
    }

    public static TagDomain of(String value) {
        if (value == null || value.isBlank()) {
            throw new IllegalArgumentException("Tag value cannot be blank.");
        }
        return new TagDomain(value.trim().toLowerCase());
    }
}
