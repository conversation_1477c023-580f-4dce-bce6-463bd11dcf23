package com.wishforthecure.forconversations.hexa.domain.model.alias;

import org.jmolecules.ddd.annotation.ValueObject;

import com.wishforthecure.forconversations.domain.enumeration.AliasType;

import lombok.AllArgsConstructor;
import lombok.Getter;

@ValueObject
@Getter
@AllArgsConstructor
public class EmailRecipientAliasDomain implements Alias {

    private final String nameSender;
    private final String emailSender;

    @Override
    public String getName() {
        return nameSender;
    }

    @Override
    public String getValue() {
        return emailSender;
    }

    @Override
    public AliasType getAliasType() {
        return AliasType.EMAIL;
    }
}
