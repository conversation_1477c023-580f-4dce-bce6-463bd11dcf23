package com.wishforthecure.forconversations.hexa.domain.model.filter;

import java.util.Set;
import java.util.stream.Collectors;

import org.jmolecules.ddd.annotation.ValueObject;

import com.wishforthecure.forconversations.hexa.domain.model.message.Message;

import lombok.Value;

@ValueObject
@Value
public class KeywordFilter implements MessageFilter {

    Set<Keyword> keywords;
    FilterMode mode;

    public static KeywordFilter of(Set<String> keywords, FilterMode mode) {
        Set<Keyword> keywordSet = keywords.stream()
                .map(Keyword::of)
                .collect(Collectors.toSet());
        return new KeywordFilter(keywordSet, mode);
    }

    @Override
    public boolean apply(Message message) {
        throw new UnsupportedOperationException("Unimplemented method 'apply'");
    }

}
