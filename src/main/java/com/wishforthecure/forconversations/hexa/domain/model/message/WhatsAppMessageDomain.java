package com.wishforthecure.forconversations.hexa.domain.model.message;

import java.time.Instant;
import java.util.List;

import org.jmolecules.ddd.annotation.ValueObject;

import com.wishforthecure.forconversations.hexa.domain.model.alias.WhatsAppRecipientAliasDomain;
import com.wishforthecure.forconversations.hexa.domain.model.alias.WhatsAppSenderAliasDomain;
import com.wishforthecure.forconversations.hexa.domain.model.feeling.FeelingDomain;
import com.wishforthecure.forconversations.hexa.domain.model.tag.TagDomain;

import lombok.AllArgsConstructor;
import lombok.Value;

@ValueObject
@Value
@AllArgsConstructor
public class WhatsAppMessageDomain implements Message {

    MessageId messageId;
    Instant time;
    WhatsAppSenderAliasDomain sender;
    WhatsAppRecipientAliasDomain recipient;
    String content;
    byte[] file;
    List<FeelingDomain> feelingList;
    List<TagDomain> tagDomainList;

    @Override
    public MessageId getMessageId() {
        return messageId;
    }

    @Override
    public Instant getTime() {
        return time;
    }

    @Override
    public String getSender() {
        return sender.getValue();
    }

    @Override
    public String getRecipients() {
        return recipient.getName();
    }

    @Override
    public String getContent() {
        return content;
    }

    @Override
    public List<FeelingDomain> getFeelingDomainList() {
        return feelingList;
    }

    @Override
    public List<TagDomain> getTagDomainList() {
        return tagDomainList;
    }
}
