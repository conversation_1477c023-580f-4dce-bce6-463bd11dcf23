package com.wishforthecure.forconversations.hexa.domain.model.filter;

import java.time.Instant;

import org.jmolecules.ddd.annotation.ValueObject;

import com.wishforthecure.forconversations.hexa.domain.model.message.Message;

import lombok.Value;

@ValueObject
@Value
public class DateRangeFilter implements MessageFilter {

    Instant startDate;
    Instant endDate;
    FilterMode mode;

    public static DateRangeFilter of(Instant startDate, Instant endDate, FilterMode mode) {
        return new DateRangeFilter(startDate, endDate, mode);
    }

    @Override
    public boolean apply(Message message) {
        throw new UnsupportedOperationException("Unimplemented method 'apply'");
    }
}
