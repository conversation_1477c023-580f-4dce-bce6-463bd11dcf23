package com.wishforthecure.forconversations.hexa.domain.service;

import java.util.List;

import com.wishforthecure.forconversations.hexa.domain.model.message.Message;
import com.wishforthecure.forconversations.hexa.domain.model.source.Source;
import com.wishforthecure.forconversations.hexa.domain.model.source.SourceType;

public interface SourceParser {
    List<Message> parse(Source source);

    SourceType getSupportedSourceType();
}