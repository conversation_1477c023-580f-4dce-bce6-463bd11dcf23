package com.wishforthecure.forconversations.hexa.domain.service;

import com.wishforthecure.forconversations.hexa.domain.model.message.Message;
import com.wishforthecure.forconversations.hexa.domain.model.source.Source;
import com.wishforthecure.forconversations.hexa.domain.model.source.SourceType;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import org.springframework.stereotype.Service;

@Service
public class SourceParsingService {

    private final Map<SourceType, SourceParser> parsers;

    public SourceParsingService(List<SourceParser> parserList) {
        this.parsers = parserList.stream().collect(Collectors.toMap(SourceParser::getSupportedSourceType, Function.identity()));
    }

    public List<Message> parse(Source source, SourceType sourceType) {
        SourceParser parser = parsers.get(sourceType);
        if (parser == null) {
            throw new IllegalArgumentException("No parser found for source type: " + sourceType);
        }
        return parser.parse(source);
    }
}

