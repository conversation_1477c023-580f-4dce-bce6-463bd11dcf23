package com.wishforthecure.forconversations.hexa.domain.model.source;

import java.time.Instant;
import java.util.List;

import org.jmolecules.ddd.annotation.AggregateRoot;

import com.wishforthecure.forconversations.hexa.domain.model.message.WhatsAppMessageDomain;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@AggregateRoot
@Data
@AllArgsConstructor
@NoArgsConstructor
public class WhatsAppSourceDomain implements Source {

    SourceId sourceId;
    Instant time;
    List<WhatsAppMessageDomain> whatsAppMessages;
    byte[] file;
    String namePersonOne;
    String namePersonTwo;

    @Override
    public byte[] getSource() {
        throw new UnsupportedOperationException("Unimplemented method 'getSource'");
    }
}
