package com.wishforthecure.forconversations.hexa.domain.model.message;

import java.time.Instant;
import java.util.List;

import com.wishforthecure.forconversations.hexa.domain.model.feeling.FeelingDomain;
import com.wishforthecure.forconversations.hexa.domain.model.tag.TagDomain;

public interface Message {
    MessageId getMessageId();

    Instant getTime();

    String getSender();

    String getRecipients();

    String getContent();

    List<FeelingDomain> getFeelingDomainList();

    List<TagDomain> getTagDomainList();
}
