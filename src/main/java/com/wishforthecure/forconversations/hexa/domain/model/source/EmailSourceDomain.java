package com.wishforthecure.forconversations.hexa.domain.model.source;

import java.time.Instant;
import java.util.List;

import org.jmolecules.ddd.annotation.Entity;

import com.wishforthecure.forconversations.hexa.domain.model.message.EmailMessageDomain;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Entity
@Getter
@AllArgsConstructor
public class EmailSourceDomain implements Source {

    SourceId sourceId;
    Instant time;
    List<EmailMessageDomain> emailMessageDomainList;
    byte[] source;
}
