package com.wishforthecure.forconversations.hexa.domain.model.conversation;

import java.time.Instant;
import java.util.List;
import java.util.Set;

import org.jmolecules.ddd.annotation.AggregateRoot;

import com.wishforthecure.forconversations.hexa.domain.model.feeling.FeelingDomain;
import com.wishforthecure.forconversations.hexa.domain.model.message.Message;
import com.wishforthecure.forconversations.hexa.domain.model.tag.TagDomain;

import lombok.AllArgsConstructor;
import lombok.Data;

@AggregateRoot
@Data
@AllArgsConstructor
public class Conversation {

    ConversationId id;
    String name;
    Instant startDate;
    Instant endDate;
    FeelingDomain feeling;
    Set<TagDomain> tags;
    List<Message> messages;

    public void assignFeeling(FeelingDomain newFeeling) {
        this.feeling = newFeeling;
    }

    public void addTag(TagDomain tag) {
        this.tags.add(tag);
    }

    public void removeTag(TagDomain tag) {
        this.tags.remove(tag);
    }

    public void rename(String newName) {
        this.name = newName;
    }
}