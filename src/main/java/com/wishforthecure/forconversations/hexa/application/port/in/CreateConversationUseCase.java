package com.wishforthecure.forconversations.hexa.application.port.in;

import com.wishforthecure.forconversations.hexa.application.dto.CreateConversationCommand;
import com.wishforthecure.forconversations.hexa.domain.model.conversation.ConversationId;
import reactor.core.publisher.Mono;

public interface CreateConversationUseCase {

    Mono<ConversationId> create(CreateConversationCommand command);
}

