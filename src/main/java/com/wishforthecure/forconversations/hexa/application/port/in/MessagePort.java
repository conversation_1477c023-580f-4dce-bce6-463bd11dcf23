package com.wishforthecure.forconversations.hexa.application.port.in;

import java.util.List;

import com.wishforthecure.forconversations.hexa.domain.model.message.EmailMessageDomain;

import reactor.core.publisher.Mono;

public interface MessagePort {
    Mono<EmailMessageDomain> save(EmailMessageDomain emailMessageDomain);

    Mono<List<EmailMessageDomain>> saveAll(List<EmailMessageDomain> messageDTOList);
}