package com.wishforthecure.forconversations.hexa.application.service;

import java.io.BufferedReader;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.time.Instant;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import org.springframework.stereotype.Service;

import com.wishforthecure.forconversations.hexa.application.port.in.WhatsAppSourcePort;
import com.wishforthecure.forconversations.hexa.application.port.out.WhatsAppSourceRepository;
import com.wishforthecure.forconversations.hexa.domain.model.alias.WhatsAppRecipientAliasDomain;
import com.wishforthecure.forconversations.hexa.domain.model.alias.WhatsAppSenderAliasDomain;
import com.wishforthecure.forconversations.hexa.domain.model.message.MessageId;
import com.wishforthecure.forconversations.hexa.domain.model.message.WhatsAppMessageDomain;
import com.wishforthecure.forconversations.hexa.domain.model.source.SourceId;
import com.wishforthecure.forconversations.hexa.domain.model.source.WhatsAppSourceDomain;
import com.wishforthecure.forconversations.hexa.infrastructure.adapter.in.controller.dto.WhatsAppSourceSaveDTO;
import com.wishforthecure.forconversations.hexa.infrastructure.adapter.in.controller.dto.WhatsAppSourceUploadDTO;

import lombok.RequiredArgsConstructor;
import reactor.core.publisher.Mono;

@Service
@RequiredArgsConstructor
public class WhatsAppSourceService implements WhatsAppSourcePort {

    private static final Pattern WHATSAPP_SENDER_PATTERN = Pattern
            .compile("^\\d{1,2}/\\d{1,2}/\\d{2,4}, \\d{1,2}:\\d{2}\\s*-\\s*([^:]+):");
    private static final Pattern WHATSAPP_MESSAGE_PATTERN = Pattern.compile(
            "^(\\d{1,2}/\\d{1,2}/\\d{2,4}),\\s*(\\d{1,2}:\\d{2})\\s*-\\s*([^:]+):\\s*(.*)");
    private static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("d/M/yy, H:m").withZone(
            ZoneId.systemDefault());

    private final WhatsAppSourceRepository whatsappSourceRepository;

    private String namePersonOne;
    private String namePersonTwo;

    @Override
    public Mono<WhatsAppSourceSaveDTO> upload(WhatsAppSourceUploadDTO whatsAppSourceUploadDTO) {
        byte[] bytes = whatsAppSourceUploadDTO.getFile();

        List<WhatsAppMessageDomain> messages = readFile(bytes);

        WhatsAppSourceDomain source = new WhatsAppSourceDomain(new SourceId(UUID.randomUUID()), Instant.now(), messages,
                bytes,
                namePersonOne, namePersonTwo);

        return whatsappSourceRepository
                .save(source)
                .map(savedSource -> new WhatsAppSourceSaveDTO(
                        savedSource.getSourceId().getValue(),
                        savedSource.getNamePersonOne(),
                        savedSource.getNamePersonTwo()));
    }

    @Override
    public Mono<WhatsAppSourceSaveDTO> save(WhatsAppSourceSaveDTO whatsAppSourceSaveDTO) {
        throw new UnsupportedOperationException("Unimplemented method 'save'");
    }

    private List<WhatsAppMessageDomain> readFile(byte[] bytes) {
        List<WhatsAppMessageDomain> messages = new ArrayList<>();
        namePersonOne = null;
        namePersonTwo = null;

        try (BufferedReader reader = new BufferedReader(
                new InputStreamReader(new ByteArrayInputStream(bytes), StandardCharsets.UTF_8))) {
            String line;
            while ((line = reader.readLine()) != null) {
                Matcher senderMatcher = WHATSAPP_SENDER_PATTERN.matcher(line);
                if (senderMatcher.find()) {
                    String currentSender = senderMatcher.group(1).trim();
                    if (namePersonOne == null) {
                        namePersonOne = currentSender;
                    } else if (namePersonTwo == null && !namePersonOne.equals(currentSender)) {
                        namePersonTwo = currentSender;
                    }
                }

                Matcher messageMatcher = WHATSAPP_MESSAGE_PATTERN.matcher(line);
                if (messageMatcher.find()) {
                    String dateStr = messageMatcher.group(1);
                    String timeStr = messageMatcher.group(2);
                    String sender = messageMatcher.group(3).trim();
                    String content = messageMatcher.group(4).trim();

                    Instant timestamp = Instant.from(DATE_TIME_FORMATTER.parse(dateStr + ", " + timeStr));

                    WhatsAppSenderAliasDomain alias = new WhatsAppSenderAliasDomain("", sender);

                    WhatsAppMessageDomain message = new WhatsAppMessageDomain(
                            new MessageId(UUID.randomUUID()),
                            timestamp,
                            alias,
                            new WhatsAppRecipientAliasDomain(null, null),
                            content,
                            null,
                            new ArrayList<>(),
                            new ArrayList<>());
                    messages.add(message);
                }
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        return messages;
    }
}
