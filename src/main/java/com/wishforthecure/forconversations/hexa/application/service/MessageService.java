package com.wishforthecure.forconversations.hexa.application.service;

import java.util.List;

import org.springframework.stereotype.Service;

import com.wishforthecure.forconversations.hexa.application.port.in.MessagePort;
import com.wishforthecure.forconversations.hexa.application.port.out.MessageRepository;
import com.wishforthecure.forconversations.hexa.domain.model.message.EmailMessageDomain;

import lombok.RequiredArgsConstructor;
import reactor.core.publisher.Mono;

@Service
@RequiredArgsConstructor
public class MessageService implements MessagePort {

    private final MessageRepository messageRepository;

    @Override
    public Mono<EmailMessageDomain> save(EmailMessageDomain emailMessageDomain) {
        return messageRepository.save(emailMessageDomain);
    }

    @Override
    public Mono<List<EmailMessageDomain>> saveAll(List<EmailMessageDomain> emailMessageDomainList) {
        return messageRepository.saveAll(emailMessageDomainList)
                .collectList();
    }
}