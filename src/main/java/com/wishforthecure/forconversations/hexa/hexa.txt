<DOCUMENT filename="forconversation_hexa.txt">
path: src/hexadecimal.md
///////////////////////////////////////////////////////////////////////////
# Arquitectura del Proyecto: DDD y Arquitectura Hexagonal

Este documento describe la arquitectura del proyecto, que se basa en los principios de **Domain-Driven Design (DDD)** y la **Arquitectura Hexagonal** (también conocida como Puertos y Adaptadores).

## 1. Conceptos Fundamentales

### Arquitectura Hexagonal (Puertos y Adaptadores)

El objetivo de esta arquitectura es aislar el núcleo de la lógica de negocio de las dependencias externas (como bases de datos, interfaces de usuario, APIs de terceros, etc.).

- **El Hexágono (El Interior):** Contiene la lógica de negocio pura, dividida en dos capas:

  1.  **`domain`**: El corazón de la aplicación. Modela el conocimiento del negocio, sus reglas y sus procesos. No depende de ninguna otra capa.
  2.  **`application`**: Orquesta los casos de uso. Actúa como un coordinador que utiliza el modelo de dominio para realizar tareas.

- **Puertos (Ports):** Son interfaces definidas en la capa de `application` que establecen contratos sobre cómo se interactúa con el núcleo de la aplicación (`puertos de entrada`) o cómo el núcleo interactúa con el exterior (`puertos de salida`).

- **Adaptadores (Adapters):** Son la implementación de los puertos y residen en la capa de `infrastructure`.
  - **Adaptadores de Entrada (Driving Adapters):** Invocan los casos de uso. Ejemplos: controladores REST, consumidores de mensajes, clientes gRPC.
  - **Adaptadores de Salida (Driven Adapters):** Son invocados por el núcleo de la aplicación. Ejemplos: repositorios de bases de datos, clientes de APIs externas.

### Domain-Driven Design (DDD)

DDD es un enfoque de desarrollo de software que se centra en modelar un dominio de negocio complejo y vincular esa implementación a los conceptos centrales del negocio. Utilizamos sus bloques de construcción (Value Objects, Entities, Aggregates) para crear un modelo de dominio rico y expresivo.

---

## 2. Estructura de Directorios

La estructura del paquete `.../hexa` refleja directamente estos conceptos arquitectónicos.

```
hexa/
├── domain/
│   ├── model/
│   │   ├── alias/
│   │   ├── conversation/
│   │   ├── filter/
│   │   └── ... (otros subdominios)
│   └── service/
├── application/
│   ├── port/
│   │   ├── in/  (Casos de Uso - Interfaces)
│   │   └── out/ (Abstracciones de Infraestructura - Interfaces)
│   ├── service/ (Implementación de Casos de Uso)
│   ├── dto/
│   └── utils/
└── infrastructure/
    └── adapter/
        ├── in/  (Controladores, Consumidores)
        └── out/ (Repositorios, Clientes API)
```

### `domain`

La capa más interna y el corazón de la aplicación.

- **`domain/model`**: Contiene los bloques de construcción de DDD:
  - **Value Objects**: Objetos inmutables definidos por sus atributos (ej: `EmailAddress`, `DateRangeFilter`).
  - **Entities**: Objetos con identidad que pueden cambiar a lo largo del tiempo.
  - **Aggregates**: Clústeres de objetos de dominio que se tratan como una única unidad.
- **`domain/service`**: Contiene lógica de negocio que no encaja de forma natural en ningún objeto del modelo.

### `application`

Orquesta la ejecución de los casos de uso. No contiene lógica de negocio.

- **`application/port/in`**: Define los casos de uso que la aplicación ofrece. Son interfaces (ej: `SearchConversationsUseCase`).
- **`application/port/out`**: Define las dependencias que la aplicación necesita del exterior (ej: `ConversationRepository`).
- **`application/service`**: Implementa las interfaces de los puertos de entrada.
- **`application/dto`**: Data Transfer Objects para transferir datos hacia y desde el exterior sin exponer el modelo de dominio.

### `infrastructure`

Contiene todo lo que interactúa con el mundo exterior.

- **`infrastructure/adapter/in`**: Implementa los puntos de entrada. Un controlador REST que recibe una petición y llama a un `UseCase` en la capa de aplicación es un adaptador de entrada.
- **`infrastructure/adapter/out`**: Implementa los puertos de salida. Una clase que implementa la interfaz `ConversationRepository` para guardar datos en MongoDB es un adaptador de salida.

---

## 3. Flujo de una Petición

Un flujo típico sigue esta secuencia:

1.  Un **Adaptador de Entrada** (ej: `ConversationController`) recibe una petición.
2.  El adaptador valida y mapea la entrada a un DTO o a tipos primitivos.
3.  Llama a un **Puerto de Entrada** (ej: `ImportConversationsUseCase`) en la capa de `application`.
4.  El **Servicio de Aplicación** (la implementación del caso de uso) orquesta la lógica:
    a. Utiliza el **Modelo de Dominio** para ejecutar las reglas de negocio.
    b. Llama a uno o más **Puertos de Salida** (ej: `ConversationRepository.save(...)`) para persistir los cambios.
5.  El **Adaptador de Salida** (ej: `MongoConversationRepository`) implementa la lógica de persistencia.
6.  El flujo regresa hasta el adaptador de entrada, que genera una respuesta.

---

## 4. Próximos Pasos Sugeridos

La base actual es muy sólida. El desarrollo debería continuar siguiendo este orden para mantener la integridad de la arquitectura:

1.  **Definir Puertos de Entrada (Casos de Uso):** Crear las interfaces en `application/port/in` para las funcionalidades principales.
2.  **Definir Puertos de Salida (Repositorios/Servicios Externos):** Crear las interfaces en `application/port/out` que necesitarán los casos de uso.
3.  **Implementar los Servicios de Aplicación:** Crear las clases en `application/service` que implementen los puertos de entrada, orquestando la lógica y llamando a los puertos de salida.
4.  **Implementar los Adaptadores de Salida:** Crear las implementaciones concretas en `infrastructure/adapter/out` (ej: repositorios para MongoDB/JPA).
5.  **Implementar los Adaptadores de Entrada:** Crear las clases en `infrastructure/adapter/in` (ej: controladores REST) que exponen los casos de uso al exterior.


path: src/application/dto/ConversationDTO.java
///////////////////////////////////////////////////////////////////////////
package com.wishforthecure.forconversations.hexa.application.dto;

import com.wishforthecure.forconversations.hexa.domain.model.conversation.ConversationId;
import com.wishforthecure.forconversations.hexa.domain.model.feeling.FeelingDomain;
import com.wishforthecure.forconversations.hexa.domain.model.tag.TagDomain;
import java.time.Instant;
import java.util.Set;
import lombok.Value;

@Value
public class ConversationDTO {

    ConversationId id;
    String name;
    Instant startDate;
    Instant endDate;
    FeelingDomain feeling;
    Set<TagDomain> tags;
}


path: src/application/dto/WhatsAppSourcePreviewDTO.java
///////////////////////////////////////////////////////////////////////////
package com.wishforthecure.forconversations.hexa.application.dto;

import com.wishforthecure.forconversations.hexa.domain.model.message.WhatsAppMessageDomain;
import java.util.List;
import lombok.Data;

@Data
public class WhatsAppSourcePreviewDTO {

    private List<WhatsAppMessageDomain> whatsappMessageList;
    private String namePersonOne;
    private String namePersonTwo;
}


path: src/application/dto/AssignFeelingCommand.java
///////////////////////////////////////////////////////////////////////////
package com.wishforthecure.forconversations.hexa.application.dto;

import com.wishforthecure.forconversations.hexa.domain.model.conversation.ConversationId;
import com.wishforthecure.forconversations.hexa.domain.model.feeling.FeelingDomain;
import lombok.Value;

@Value
public class AssignFeelingCommand {

    ConversationId conversationId;
    FeelingDomain feeling;
}


path: src/application/dto/CreateConversationCommand.java
///////////////////////////////////////////////////////////////////////////
package com.wishforthecure.forconversations.hexa.application.dto;

import com.wishforthecure.forconversations.hexa.domain.model.source.SourceId;
import com.wishforthecure.forconversations.hexa.domain.model.source.SourceType;
import lombok.Value;

@Value
public class CreateConversationCommand {

    String conversationName;
    SourceId sourceId;
    SourceType sourceType;
}


path: src/application/dto/AddTagCommand.java
///////////////////////////////////////////////////////////////////////////
package com.wishforthecure.forconversations.hexa.application.dto;

import com.wishforthecure.forconversations.hexa.domain.model.conversation.ConversationId;
    lombok.Value;

@Value
public class AddTagCommand {

    ConversationId conversationId;
    String tagName;
}


path: src/application/dto/RenameConversationCommand.java
///////////////////////////////////////////////////////////////////////////
package com.wishforthecure.forconversations.hexa.application.dto;

import com.wishforthecure.forconversations.hexa.domain.model.conversation.ConversationId;
    lombok.Value;

@Value
public class RenameConversationCommand {

    ConversationId conversationId;
    String newName;
}


path: src/application/dto/RemoveTagCommand.java
///////////////////////////////////////////////////////////////////////////
package com.wishforthecure.forconversations.hexa.application.dto;

import com.wishforthecure.forconversations.hexa.domain.model.conversation.ConversationId;
    lombok.Value;

@Value
public class RemoveTagCommand {

    ConversationId conversationId;
    String tagName;
}


path: src/application/utils/MBoxUtils.java
///////////////////////////////////////////////////////////////////////////
package com.wishforthecure.forconversations.hexa.application.utils;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
    import java.util.Properties;
import java.util.UUID;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import org.apache.james.mime4j.mboxiterator.CharBufferWrapper;
import org.apache.james.mime4j.mboxiterator.MboxIterator;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import com.wishforthecure.forconversations.hexa.domain.model.alias.EmailAliasDomain;
import com.wishforthecure.forconversations.hexa.domain.model.message.EmailMessageDomain;
import com.wishforthecure.forconversations.hexa.domain.model.message.MessageId;
import com.wishforthecure.forconversations.hexa.domain.model.source.SourceType;
import com.wishforthecure.forconversations.hexa.domain.model.tag.TagDomain;
import com.wishforthecure.forconversations.hexa.domain.model.feeling.FeelingDomain;

import jakarta.mail.Address;
import jakarta.mail.Message;
import jakarta.mail.MessagingException;
import jakarta.mail.Multipart;
import jakarta.mail.Part;
import jakarta.mail.Session;
import jakarta.mail.internet.MimeMessage;

@Component
public class MBoxUtils {

    private static final Logger LOG = LoggerFactory.getLogger(MBoxUtils.class);

    public List<EmailMessageDomain> parse(byte[] mboxBytes) {
        List<EmailMessageDomain> emailList = new ArrayList<>();
        final Session session = Session.getDefaultInstance(new Properties());

        if (mboxBytes == null || mboxBytes.length == 0) {
            LOG.debug("Empty or null MBOX content provided");
            return emailList;
        }

        LOG.debug("Processing MBOX file with {} bytes", mboxBytes.length);

        String preview = new String(mboxBytes, StandardCharsets.UTF_8);
        String[] lines = preview.split("\n", 5);
        LOG.debug("First few lines of MBOX:");
        for (int i = 0; i < Math.min(lines.length, 3); i++) {
            LOG.debug("Line {}: '{}'", i + 1, lines[i]);
        }

        Path tempFile = null;
        try {
            tempFile = Files.createTempFile("mbox", ".tmp");
            try (OutputStream os = Files.newOutputStream(tempFile)) {
                os.write(mboxBytes);
            }

            LOG.debug("Created temporary file: {}", tempFile.toAbsolutePath());

            int messageCount = 0;
            for (final CharBufferWrapper messageCharBuffer : MboxIterator.fromFile(tempFile.toFile())
                    .charset(StandardCharsets.UTF_8)
                    .build()) {
                messageCount++;
                LOG.debug("Processing message #{}", messageCount);

                EmailMessageDomain dto = processMessage(messageCharBuffer, session);
                if (dto != null) {
                    emailList.add(dto);
                }
            }

            LOG.info("Successfully parsed {} messages from MBOX file", emailList.size());
        } catch (IllegalArgumentException e) {
            LOG.warn("Invalid MBOX format detected: {}", e.getMessage());
            LOG.debug("MBOX content preview (first 500 chars): {}",
                    preview.length() > 500 ? preview.substring(0, 500) + "..." : preview);
        } catch (IOException e) {
            LOG.error("Error al leer el stream del MBOX: {}", e.getMessage(), e);
        } finally {
            if (tempFile != null && Files.exists(tempFile)) {
                try {
                    Files.delete(tempFile);
                } catch (IOException e) {
                    LOG.warn("Failed to delete temporary file: {}", tempFile.toAbsolutePath(), e);
                }
            }
        }

        return emailList;
    }

    private EmailMessageDomain processMessage(CharBufferWrapper messageCharBuffer, Session session) {
        try {
            String messageContent = messageCharBuffer.toString();
            InputStream messageInputStream = new ByteArrayInputStream(messageContent.getBytes(StandardCharsets.UTF_8));
            MimeMessage message = new MimeMessage(session, messageInputStream);

            String messageIdStr = message.getMessageID();
            MessageId messageId = new MessageId(
                    messageIdStr != null ? UUID.nameUUIDFromBytes(messageIdStr.getBytes()) : UUID.randomUUID());

            Date sentDate = message.getSentDate();
            Instant time = sentDate != null ? sentDate.toInstant() : Instant.now();

            Address[] fromAddresses = message.getFrom();
            String senderStr = fromAddresses != null && fromAddresses.length > 0 ? fromAddresses[0].toString() : "";
            EmailAliasDomain sender = new EmailAliasDomain(null, null, senderStr, null);

            List<EmailAliasDomain> recipients = getAllRecipients(message);

            String subject = message.getSubject() != null ? message.getSubject() : "";
            String body = getTextFromMessage(message);
            String content = subject + "##_##" + body;

            return new EmailMessageDomain(messageId, time, sender, recipients, content,
                    new ArrayList<>(), new ArrayList<>(), SourceType.EMAIL);
        } catch (MessagingException | IOException e) {
            LOG.warn("Error procesando un mensaje, se saltará: {}", e.getMessage(), e);
            return null;
        }
    }

    private String getTextFromMessage(Message message) throws MessagingException, IOException {
        if (message.isMimeType("text/plain")) {
            return message.getContent().toString();
        }
        if (message.isMimeType("multipart/*")) {
            return getTextFromMultipart((Multipart) message.getContent());
        }
        return "";
    }

    private String getTextFromMultipart(Multipart multipart) throws MessagingException, IOException {
        StringBuilder result = new StringBuilder();
        for (int i = 0; i < multipart.getCount(); i++) {
            Part bodyPart = multipart.getBodyPart(i);
            if (bodyPart.isMimeType("text/plain")) {
                return bodyPart.getContent().toString();
            } else if (bodyPart.isMimeType("text/html")) {
            } else if (bodyPart.getContent() instanceof Multipart nestedMultipart) {
                String nestedText = getTextFromMultipart(nestedMultipart);
                if (!nestedText.isEmpty()) {
                    return nestedText;
                }
            }
        }
        return result.toString();
    }

    private List<EmailAliasDomain> getAllRecipients(Message message) throws MessagingException {
        Stream<Address> to = safeStream(message.getRecipients(Message.RecipientType.TO));
        Stream<Address> cc = safeStream(message.getRecipients(Message.RecipientType.CC));
        Stream<Address> bcc = safeStream(message.getRecipients(Message.RecipientType.BCC));

        return Stream.concat(to, Stream.concat(cc, bcc))
                .map(addr -> new EmailAliasDomain(null, null, null, addr.toString()))
                .collect(Collectors.toList());
    }

    private Stream<Address> safeStream(Address[] addresses) {
        return addresses == null ? Stream.empty() : Arrays.stream(addresses);
    }
}

path: src/application/service/ImportConversationsService.java
///////////////////////////////////////////////////////////////////////////
package com.wishforthecure.forconversations.hexa.application.service;

import com.wishforthecure.forconversations.hexa.application.port.in.ImportConversationsUseCase;
import com.wishforthecure.forconversations.hexa.application.port.out.ConversationRepository;
import com.wishforthecure.forconversations.hexa.application.utils.MBoxUtils;
import com.wishforthecure.forconversations.hexa.domain.model.conversation.Conversation;
import com.wishforthecure.forconversations.hexa.domain.model.conversation.ConversationId;
import com.wishforthecure.forconversations.hexa.domain.model.message.EmailMessageDomain;
import java.util.List;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class ImportConversationsService implements ImportConversationsUseCase {

    private final MBoxUtils mboxUtils;
    private final ConversationRepository conversationRepository;

    @Override
    public void importFromMbox(byte[] mboxFileBytes) {
        List<EmailMessageDomain> messages = mboxUtils.parse(mboxFileBytes);

        if (!messages.isEmpty()) {
            Conversation conversation = new Conversation(
                new ConversationId(UUID.randomUUID()),
                messages.get(0).getContent().split("##_##")[0],
                messages.stream().map(Message::getTime).min(Instant::compareTo).orElse(Instant.now()),
                messages.stream().map(Message::getTime).max(Instant::compareTo).orElse(Instant.now()),
                null,
                new HashSet<>(),
                (List) messages
            );
            conversationRepository.save(conversation).block();
        }
    }
}

path: src/application/service/ListConversationsService.java
///////////////////////////////////////////////////////////////////////////
package com.wishforthecure.forconversations.hexa.application.service;

import com.wishforthecure.forconversations.hexa.application.dto.ConversationDTO;
import com.wishforthecure.forconversations.hexa.application.port.in.ListConversationsUseCase;
import com.wishforthecure.forconversations.hexa.application.port.out.ConversationRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;

@Service
@RequiredArgsConstructor
public class ListConversationsService implements ListConversationsUseCase {

    private final ConversationRepository conversationRepository;

    @Override
    public Flux<ConversationDTO> listAll() {
        return conversationRepository.findAll()
                .map(conversation -> new ConversationDTO(
                        conversation.getId(),
                        conversation.getName(),
                        conversation.getStartDate(),
                        conversation.getEndDate(),
                        conversation.getFeeling(),
                        conversation.getTags()
                ));
    }
}

path: src/application/service/RenameConversationService.java
///////////////////////////////////////////////////////////////////////////
package com.wishforthecure.forconversations.hexa.application.service;

import com.wishforthecure.forconversations.hexa.application.port.in.RenameConversationUseCase;
import com.wishforthecure.forconversations.hexa.application.port.out.ConversationRepository;
import com.wishforthecure.forconversations.hexa.domain.model.conversation.ConversationId;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

@Service
@RequiredArgsConstructor
public class RenameConversationService implements RenameConversationUseCase {

    private final ConversationRepository conversationRepository;

    @Override
    public Mono<Void> rename(ConversationId conversationId, String newName) {
        return conversationRepository.findById(conversationId)
                .switchIfEmpty(Mono.error(new IllegalArgumentException("Conversation not found")))
                .map(conversation -> {
                    conversation.rename(newName);
                    return conversation;
                })
                .flatMap(conversationRepository::save)
                .then();
    }
}

path: src/application/service/SourceService.java
///////////////////////////////////////////////////////////////////////////
package com.wishforthecure.forconversations.hexa.application.service;

import com.wishforthecure.forconversations.hexa.application.port.in.SourcePort;
import com.wishforthecure.forconversations.hexa.application.port.out.SourceRepository;
import com.wishforthecure.forconversations.hexa.domain.model.source.Source;
import com.wishforthecure.forconversations.service.dto.SourceDTO;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

@Service
@RequiredArgsConstructor
public class SourceService implements SourcePort {

    private final SourceRepository sourceRepository;

    @Override
    public Mono<SourceDTO> save(SourceDTO sourceDTO) {
        Source source = mapToDomain(sourceDTO);
        return sourceRepository.save(source)
                .map(this::mapToDTO);
    }

    private Source mapToDomain(SourceDTO dto) {
        throw new UnsupportedOperationException("Mapping not implemented");
    }

    private SourceDTO mapToDTO(Source source) {
        throw new UnsupportedOperationException("Mapping not implemented");
    }
}

path: src/application/service/RemoveTagService.java
///////////////////////////////////////////////////////////////////////////
package com.wishforthecure.forconversations.hexa.application.service;

import com.wishforthecure.forconversations.hexa.application.port.in.RemoveTagUseCase;
import com.wishforthecure.forconversations.hexa.application.port.out.ConversationRepository;
import com.wishforthecure.forconversations.hexa.domain.model.conversation.ConversationId;
import com.wishforthecure.forconversations.hexa.domain.model.tag.TagDomain;
    lombok.RequiredArgsConstructor;
    org.springframework.stereotype.Service;
    reactor.core.publisher.Mono;

@Service
@RequiredArgsConstructor
public class RemoveTagService implements RemoveTagUseCase {

    private final ConversationRepository conversationRepository;

    @Override
    public Mono<Void> removeTag(ConversationId conversationId, String tagName) {
        return conversationRepository.findById(conversationId)
                .switchIfEmpty(Mono.error(new IllegalArgumentException("Conversation not found")))
                .map(conversation -> {
                    conversation.removeTag(TagDomain.of(tagName));
                    return conversation;
                })
                .flatMap(conversationRepository::save)
                .then();
    }
}

path: src/application/service/AssignFeelingService.java
///////////////////////////////////////////////////////////////////////////
package com.wishforthecure.forconversations.hexa.application.service;

import com.wishforthecure.forconversations.hexa.application.port.in.AssignFeelingUseCase;
import com.wishforthecure.forconversations.hexa.application.port.out.ConversationRepository;
import com.wishforthecure.forconversations.hexa.domain.model.conversation.ConversationId;
import com.wishforthecure.forconversations.hexa.domain.model.feeling.FeelingDomain;
    lombok.RequiredArgsConstructor;
    org.springframework.stereotype.Service;
    reactor.core.publisher.Mono;

@Service
@RequiredArgsConstructor
public class AssignFeelingService implements AssignFeelingUseCase {

    private final ConversationRepository conversationRepository;

    @Override
    public Mono<Void> assignFeeling(ConversationId conversationId, FeelingDomain feeling) {
        return conversationRepository.findById(conversationId)
                .switchIfEmpty(Mono.error(new IllegalArgumentException("Conversation not found")))
                .map(conversation -> {
                    conversation.assignFeeling(feeling);
                    return conversation;
                })
                .flatMap(conversationRepository::save)
                .then();
    }
}

path: src/application/service/CreateConversationService.java
///////////////////////////////////////////////////////////////////////////
package com.wishforthecure.forconversations.hexa.application.service;

import com.wishforthecure.forconversations.hexa.application.dto.CreateConversationCommand;
import com.wishforthecure.forconversations.hexa.application.port.in.CreateConversationUseCase;
import com.wishforthecure.forconversations.hexa.application.port.out.ConversationRepository;
import com.wishforthecure.forconversations.hexa.application.port.out.SourceRepository;
import com.wishforthecure.forconversations.hexa.domain.model.conversation.Conversation;
import com.wishforthecure.forconversations.hexa.domain.model.conversation.ConversationId;
import com.wishforthecure.forconversations.hexa.domain.model.message.Message;
import com.wishforthecure.forconversations.hexa.domain.service.SourceParsingService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

import java.util.List;
import java.util.UUID;
import java.util.HashSet;

@Service
@RequiredArgsConstructor
public class CreateConversationService implements CreateConversationUseCase {

    private final SourceRepository sourceRepository;
    private final SourceParsingService parsingService;
    private final ConversationRepository conversationRepository;

    @Override
    public Mono<ConversationId> create(CreateConversationCommand command) {
        return sourceRepository.findById(command.getSourceId())
                .switchIfEmpty(Mono.error(new IllegalArgumentException("Source not found")))
                .flatMap(source -> {
                    List<Message> messages = parsingService.parse(source, command.getSourceType());
                    Conversation conversation = new Conversation(
                            new ConversationId(UUID.randomUUID()),
                            command.getConversationName(),
                            messages.stream().map(Message::getTime).min(Instant::compareTo).orElse(Instant.now()),
                            messages.stream().map(Message::getTime).max(Instant::compareTo).orElse(Instant.now()),
                            null,
                            new HashSet<>(),
                            messages
                    );
                    return conversationRepository.save(conversation).thenReturn(conversation.getId());
                });
    }
}

path: src/application/service/AddTagService.java
///////////////////////////////////////////////////////////////////////////
package com.wishforthecure.forconversations.hexa.application.service;

import com.wishforthecure.forconversations.hexa.application.port.in.AddTagUseCase;
import com.wishforthecure.forconversations.hexa.application.port.out.ConversationRepository;
import com.wishforthecure.forconversations.hexa.domain.model.conversation.ConversationId;
import com.wishforthecure.forconversations.hexa.domain.model.tag.TagDomain;
    lombok.RequiredArgsConstructor;
    org.springframework.stereotype.Service;
    reactor.core.publisher.Mono;

@Service
@RequiredArgsConstructor
public class AddTagService implements AddTagUseCase {

    private final ConversationRepository conversationRepository;

    @Override
    public Mono<Void> addTag(ConversationId conversationId, String tagName) {
        return conversationRepository.findById(conversationId)
                .switchIfEmpty(Mono.error(new IllegalArgumentException("Conversation not found")))
                .map(conversation -> {
                    conversation.addTag(TagDomain.of(tagName));
                    return conversation;
                })
                .flatMap(conversationRepository::save)
                .then();
    }
}

path: src/application/service/EmailSourceService.java
///////////////////////////////////////////////////////////////////////////
package com.wishforthecure.forconversations.hexa.application.service;

import java.time.Instant;
import java.util.List;
import java.util.UUID;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.wishforthecure.forconversations.hexa.application.port.in.EmailSourcePort;
import com.wishforthecure.forconversations.hexa.application.port.out.EmailSourceRepository;
import com.wishforthecure.forconversations.hexa.application.utils.MBoxUtils;
import com.wishforthecure.forconversations.hexa.domain.model.message.EmailMessageDomain;
import com.wishforthecure.forconversations.hexa.domain.model.source.EmailSourceDomain;
import com.wishforthecure.forconversations.hexa.domain.model.source.SourceId;
import com.wishforthecure.forconversations.hexa.infrastructure.adapter.in.controller.dto.EmailSourceSaveDTO;
import com.wishforthecure.forconversations.hexa.infrastructure.adapter.in.controller.dto.EmailSourceUploadDTO;

import lombok.RequiredArgsConstructor;
import reactor.core.publisher.Mono;

@Service
@RequiredArgsConstructor
public class EmailSourceService implements EmailSourcePort {

    private static final Logger LOG = LoggerFactory.getLogger(EmailSourceService.class);

    private final EmailSourceRepository emailSourceRepository;
    private final MBoxUtils mboxUtils;

    @Override
    public Mono<EmailSourceSaveDTO> upload(EmailSourceUploadDTO emailSourceUploadDTO) {
        byte[] bytes = emailSourceUploadDTO.getFile();

        List<EmailMessageDomain> messages = mboxUtils.parse(bytes);

        LOG.debug("Parsed {} email messages from MBOX file", messages.size());

        EmailSourceDomain source = new EmailSourceDomain(new SourceId(UUID.randomUUID()), Instant.now(), messages,
                bytes);

        return emailSourceRepository
                .save(source)
                .map(savedSource -> new EmailSourceSaveDTO(
                        savedSource.getSourceId().getValue().toString(),
                        savedSource.getEmailMessageDomainList().size()));
    }

    @Override
    public Mono<EmailSourceSaveDTO> save(EmailSourceSaveDTO emailSourceSaveDTO) {
        throw new UnsupportedOperationException("Unimplemented method 'save'");
    }
}

path: src/application/service/WhatsAppSourceService.java
///////////////////////////////////////////////////////////////////////////
package com.wishforthecure.forconversations.hexa.application.service;

import java.io.BufferedReader;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.time.Instant;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import org.springframework.stereotype.Service;

import com.wishforthecure.forconversations.hexa.application.port.in.WhatsAppSourcePort;
import com.wishforthecure.forconversations.hexa.application.port.out.WhatsAppSourceRepository;
import com.wishforthecure.forconversations.hexa.domain.model.alias.WhatsAppAliasDomain;
import com.wishforthecure.forconversations.hexa.domain.model.message.MessageId;
import com.wishforthecure.forconversations.hexa.domain.model.message.WhatsAppMessageDomain;
import com.wishforthecure.forconversations.hexa.domain.model.source.SourceId;
import com.wishforthecure.forconversations.hexa.domain.model.source.WhatsAppSourceDomain;
import com.wishforthecure.forconversations.hexa.infrastructure.adapter.in.controller.dto.WhatsAppSourceSaveDTO;
import com.wishforthecure.forconversations.hexa.infrastructure.adapter.in.controller.dto.WhatsAppSourceUploadDTO;

import lombok.RequiredArgsConstructor;
import reactor.core.publisher.Mono;

@Service
@RequiredArgsConstructor
public class WhatsAppSourceService implements WhatsAppSourcePort {

    private static final Pattern WHATSAPP_SENDER_PATTERN = Pattern
            .compile("^\\d{1,2}/\\d{1,2}/\\d{2,4}, \\d{1,2}:\\d{2}\\s*-\\s*([^:]+):");
    private static final Pattern WHATSAPP_MESSAGE_PATTERN = Pattern.compile(
            "^(\\d{1,2}/\\d{1,2}/\\d{2,4}),\\s*(\\d{1,2}:\\d{2})\\s*-\\s*([^:]+):\\s*(.*)");
    private static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("d/M/yy, H:m").withZone(
            ZoneId.systemDefault());

    private final WhatsAppSourceRepository whatsappSourceRepository;

    private String namePersonOne;
    private String namePersonTwo;

    @Override
    public Mono<WhatsAppSourceSaveDTO> upload(WhatsAppSourceUploadDTO whatsAppSourceUploadDTO) {
        byte[] bytes = whatsAppSourceUploadDTO.getFile();

        List<WhatsAppMessageDomain> messages = readFile(bytes);

        WhatsAppSourceDomain source = new WhatsAppSourceDomain(new SourceId(UUID.randomUUID()), Instant.now(), messages, bytes,
                namePersonOne, namePersonTwo);

        return whatsappSourceRepository
                .save(source)
                .map(savedSource -> new WhatsAppSourceSaveDTO(
                        savedSource.getSourceId().getValue(),
                        savedSource.getNamePersonOne(),
                        savedSource.getNamePersonTwo()
                ));
    }

    @Override
    public Mono<WhatsAppSourceSaveDTO> save(WhatsAppSourceSaveDTO whatsAppSourceSaveDTO) {
        throw new UnsupportedOperationException("Unimplemented method 'save'");
    }

    private List<WhatsAppMessageDomain> readFile(byte[] bytes) {
        List<WhatsAppMessageDomain> messages = new ArrayList<>();
        namePersonOne = null;
        namePersonTwo = null;

        try (BufferedReader reader = new BufferedReader(
                new InputStreamReader(new ByteArrayInputStream(bytes), StandardCharsets.UTF_8))) {
            String line;
            while ((line = reader.readLine()) != null) {
                Matcher senderMatcher = WHATSAPP_SENDER_PATTERN.matcher(line);
                if (senderMatcher.find()) {
                    String currentSender = senderMatcher.group(1).trim();
                    if (namePersonOne == null) {
                        namePersonOne = currentSender;
                    } else if (namePersonTwo == null && !namePersonOne.equals(currentSender)) {
                        namePersonTwo = currentSender;
                    }
                }

                Matcher messageMatcher = WHATSAPP_MESSAGE_PATTERN.matcher(line);
                if (messageMatcher.find()) {
                    String dateStr = messageMatcher.group(1);
                    String timeStr = messageMatcher.group(2);
                    String senderStr = messageMatcher.group(3).trim();
                    String content = messageMatcher.group(4).trim();

                    Instant timestamp = Instant.from(DATE_TIME_FORMATTER.parse(dateStr + ", " + timeStr));

                    WhatsAppAliasDomain alias = new WhatsAppAliasDomain(null, null, senderStr, null);

                    WhatsAppMessageDomain message = new WhatsAppMessageDomain(
                            new MessageId(UUID.randomUUID()),
                            timestamp,
                            alias,
                            content,
                            null,
                            new ArrayList<>(),
                            new ArrayList<>()
                    );
                    messages.add(message);
                }
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        return messages;
    }
}

path: src/application/service/MessageService.java
///////////////////////////////////////////////////////////////////////////
package com.wishforthecure.forconversations.hexa.application.service;

import com.wishforthecure.forconversations.hexa.application.port.in.MessagePort;
import com.wishforthecure.forconversations.hexa.application.port.out.MessageRepository;
import com.wishforthecure.forconversations.hexa.domain.model.message.EmailMessageDomain;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.List;

@Service
@RequiredArgsConstructor
public class MessageService implements MessagePort {

    private final MessageRepository messageRepository;

    @Override
    public Mono<EmailMessageDomain> save(EmailMessageDomain emailMessageDomain) {
        return messageRepository.save(emailMessageDomain);
    }

    @Override
    public Mono<List<EmailMessageDomain>> saveAll(List<EmailMessageDomain> emailMessageDomainList) {
        return messageRepository.saveAll(emailMessageDomainList)
                .collectList();
    }
}

path: src/application/port/out/ConversationRepository.java
///////////////////////////////////////////////////////////////////////////
package com.wishforthecure.forconversations.hexa.application.port.out;

import com.wishforthecure.forconversations.hexa.domain.model.conversation.Conversation;
import com.wishforthecure.forconversations.hexa.domain.model.conversation.ConversationId;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

public interface ConversationRepository {

    Mono<Void> save(Conversation conversation);

    Mono<Conversation> findById(ConversationId conversationId);

    Flux<Conversation> findAll();
}

path: src/application/port/out/MessageRepository.java
///////////////////////////////////////////////////////////////////////////
package com.wishforthecure.forconversations.hexa.application.port.out;

import com.wishforthecure.forconversations.hexa.domain.model.message.EmailMessageDomain;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.List;

public interface MessageRepository {

    Mono<EmailMessageDomain> save(EmailMessageDomain emailMessageDomain);

    Flux<EmailMessageDomain> saveAll(List<EmailMessageDomain> emailMessageDomainList);
}

path: src/application/port/out/SearchConversationsUseCase.java
///////////////////////////////////////////////////////////////////////////
package com.wishforthecure.forconversations.hexa.application.port.out;

import com.wishforthecure.forconversations.hexa.application.port.in.dto.ConversationSummaryDTO;
import com.wishforthecure.forconversations.hexa.application.port.in.dto.SearchCriteriaDTO;
import java.util.List;

public interface SearchConversationsUseCase {

    List<ConversationSummaryDTO> search(SearchCriteriaDTO criteria);
}

path: src/application/port/out/WhatsAppSourceRepository.java
///////////////////////////////////////////////////////////////////////////
package com.wishforthecure.forconversations.hexa.application.port.out;

import com.wishforthecure.forconversations.hexa.domain.model.source.WhatsAppSourceDomain;
import reactor.core.publisher.Mono;

public interface WhatsAppSourceRepository {

    Mono<WhatsAppSourceDomain> save(WhatsAppSourceDomain whatsAppSourceDomain);

    Mono<WhatsAppSourceDomain> findById(String id);
}

path: src/application/port/out/SourceRepository.java
///////////////////////////////////////////////////////////////////////////
package com.wishforthecure.forconversations.hexa.application.port.out;

import com.wishforthecure.forconversations.hexa.domain.model.source.Source;
import com.wishforthecure.forconversations.hexa.domain.model.source.SourceId;
import reactor.core.publisher.Mono;

public interface SourceRepository {

    Mono<Source> save(Source source);

    Mono<Source> findById(SourceId id);
}

path: src/application/port/out/EmailSourceRepository.java
///////////////////////////////////////////////////////////////////////////
package com.wishforthecure.forconversations.hexa.application.port.out;

import com.wishforthecure.forconversations.hexa.domain.model.source.EmailSourceDomain;

import reactor.core.publisher.Mono;

public interface EmailSourceRepository {

    Mono<EmailSourceDomain> save(EmailSourceDomain emailSourceDomain);

    Mono<EmailSourceDomain> findById(String id);
}

path: src/application/port/in/AssignFeelingUseCase.java
///////////////////////////////////////////////////////////////////////////
package com.wishforthecure.forconversations.hexa.application.port.in;

import com.wishforthecure.forconversations.hexa.domain.model.conversation.ConversationId;
import com.wishforthecure.forconversations.hexa.domain.model.feeling.FeelingDomain;
import reactor.core.publisher.Mono;

public interface AssignFeelingUseCase {

    Mono<Void> assignFeeling(ConversationId conversationId, FeelingDomain feeling);
}

path: src/application/port/in/CreateConversationUseCase.java
///////////////////////////////////////////////////////////////////////////
package com.wishforthecure.forconversations.hexa.application.port.in;

import com.wishforthecure.forconversations.hexa.application.dto.CreateConversationCommand;
import com.wishforthecure.forconversations.hexa.domain.model.conversation.ConversationId;
import reactor.core.publisher.Mono;

public interface CreateConversationUseCase {

    Mono<ConversationId> create(CreateConversationCommand command);
}

path: src/application/port/in/AddTagUseCase.java
///////////////////////////////////////////////////////////////////////////
package com.wishforthecure.forconversations.hexa.application.port.in;

import com.wishforthecure.forconversations.hexa.domain.model.conversation.ConversationId;
import reactor.core.publisher.Mono;

public interface AddTagUseCase {

    Mono<Void> addTag(ConversationId conversationId, String tagName);
}

path: src/application/port/in/RenameConversationUseCase.java
///////////////////////////////////////////////////////////////////////////
package com.wishforthecure.forconversations.hexa.application.port.in;

import com.wishforthecure.forconversations.hexa.domain.model.conversation.ConversationId;
import reactor.core.publisher.Mono;

public interface RenameConversationUseCase {

    Mono<Void> rename(ConversationId conversationId, String newName);
}

path: src/application/port/in/RemoveTagUseCase.java
///////////////////////////////////////////////////////////////////////////
package com.wishforthecure.forconversations.hexa.application.port.in;

import com.wishforthecure.forconversations.hexa.domain.model.conversation.ConversationId;
import reactor.core.publisher.Mono;

public interface RemoveTagUseCase {

    Mono<Void> removeTag(ConversationId conversationId, String tagName);
}

path: src/application/port/in/ListConversationsUseCase.java
///////////////////////////////////////////////////////////////////////////
package com.wishforthecure.forconversations.hexa.application.port.in;

import com.wishforthecure.forconversations.hexa.application.dto.ConversationDTO;
import reactor.core.publisher.Flux;

public interface ListConversationsUseCase {

    Flux<ConversationDTO> listAll();
}

path: src/application/port/in/ImportConversationsUseCase.java
///////////////////////////////////////////////////////////////////////////
package com.wishforthecure.forconversations.hexa.application.port.in;

public interface ImportConversationsUseCase {

    void importFromMbox(byte[] mboxFileBytes);
}

path: src/application/port/in/SourcePort.java
///////////////////////////////////////////////////////////////////////////
package com.wishforthecure.forconversations.hexa.application.port.in;

import com.wishforthecure.forconversations.service.dto.SourceDTO;
import reactor.core.publisher.Mono;

public interface SourcePort {
    Mono<SourceDTO> save(SourceDTO sourceDTO);
}

path: src/application/port/in/MessagePort.java
///////////////////////////////////////////////////////////////////////////
package com.wishforthecure.forconversations.hexa.application.port.in;

import java.util.List;

import com.wishforthecure.forconversations.hexa.domain.model.message.EmailMessageDomain;

import reactor.core.publisher.Mono;

public interface MessagePort {
    Mono<EmailMessageDomain> save(EmailMessageDomain emailMessageDomain);

    Mono<List<EmailMessageDomain>> saveAll(List<EmailMessageDomain> messageDTOList);
}

path: src/application/port/in/EmailSourcePort.java
///////////////////////////////////////////////////////////////////////////
package com.wishforthecure.forconversations.hexa.application.port.in;

import com.wishforthecure.forconversations.hexa.infrastructure.adapter.in.controller.dto.EmailSourceSaveDTO;
import com.wishforthecure.forconversations.hexa.infrastructure.adapter.in.controller.dto.EmailSourceUploadDTO;
import reactor.core.publisher.Mono;

public interface EmailSourcePort {
    Mono<EmailSourceSaveDTO> upload(EmailSourceUploadDTO emailSourceUploadDTO);

    Mono<EmailSourceSaveDTO> save(EmailSourceSaveDTO emailSourceSaveDTO);
}

path: src/application/port/in/WhatsAppSourcePort.java
///////////////////////////////////////////////////////////////////////////
package com.wishforthecure.forconversations.hexa.application.port.in;

import com.wishforthecure.forconversations.hexa.infrastructure.adapter.in.controller.dto.WhatsAppSourceSaveDTO;
import com.wishforthecure.forconversations.hexa.infrastructure.adapter.in.controller.dto.WhatsAppSourceUploadDTO;
import reactor.core.publisher.Mono;

public interface WhatsAppSourcePort {
    Mono<WhatsAppSourceSaveDTO> upload(WhatsAppSourceUploadDTO whatsAppSourceUploadDTO);
    Mono<WhatsAppSourceSaveDTO> save(WhatsAppSourceSaveDTO whatsAppSourceSaveDTO);
}

path: src/application/port/in/GetConversationDetailsUseCase.java
///////////////////////////////////////////////////////////////////////////
package com.wishforthecure.forconversations.hexa.application.port.in;

import com.wishforthecure.forconversations.hexa.application.port.in.dto.ConversationDetailsDTO;
import com.wishforthecure.forconversations.hexa.domain.model.conversation.ConversationId;

public interface GetConversationDetailsUseCase {

    ConversationDetailsDTO getById(ConversationId conversationId);
}

path: src/application/port/in/dto/SearchCriteriaDTO.java
///////////////////////////////////////////////////////////////////////////
package com.wishforthecure.forconversations.hexa.application.port.in.dto;

import java.time.Instant;
import java.util.Set;

public record SearchCriteriaDTO(
    Set<String> addresses,
    Instant startDate,
    Instant endDate
) {}

path: src/application/port/in/dto/ConversationDetailsDTO.java
///////////////////////////////////////////////////////////////////////////
package com.wishforthecure.forconversations.hexa.application.port.in.dto;

import java.time.Instant;
import java.util.List;

public record ConversationDetailsDTO(
    String conversationId,
    String title,
    List<MessageDTO> messages
) {}

path: src/application/port/in/dto/MessageDTO.java
///////////////////////////////////////////////////////////////////////////
package com.wishforthecure.forconversations.hexa.application.port.in.dto;

import java.time.Instant;
import java.util.List;

public record MessageDTO(
    String messageId,
    Instant timestamp,
    String sender,
    List<String> recipients,
    String content
) {}

path: src/application/port/in/dto/ConversationSummaryDTO.java
///////////////////////////////////////////////////////////////////////////
package com.wishforthecure.forconversations.hexa.application.port.in.dto;

import java.time.Instant;

public record ConversationSummaryDTO(
    String conversationId,
    String title,
    Instant lastMessageTimestamp
) {}

path: src/infrastructure/adapter/out/persistence/repository/WhatsAppSourceMongoRepository.java
///////////////////////////////////////////////////////////////////////////
package com.wishforthecure.forconversations.hexa.infrastructure.adapter.out.persistence.repository;

import com.wishforthecure.forconversations.hexa.infrastructure.adapter.out.persistence.entity.WhatsAppSourceEntity;
import java.util.UUID;
import org.springframework.data.mongodb.repository.ReactiveMongoRepository;

public interface WhatsAppSourceMongoRepository extends ReactiveMongoRepository<WhatsAppSourceEntity, UUID> {
}

path: src/infrastructure/adapter/out/persistence/repository/ConversationMongoRepository.java
///////////////////////////////////////////////////////////////////////////
package com.wishforthecure.forconversations.hexa.infrastructure.adapter.out.persistence.repository;

import com.wishforthecure.forconversations.hexa.infrastructure.adapter.out.persistence.entity.ConversationEntity;
import org.springframework.data.mongodb.repository.ReactiveMongoRepository;

import java.util.UUID;

public interface ConversationMongoRepository extends ReactiveMongoRepository<ConversationEntity, UUID> {
}

path: src/infrastructure/adapter/out/persistence/repository/EmailSourceMongoRepository.java
///////////////////////////////////////////////////////////////////////////
package com.wishforthecure.forconversations.hexa.infrastructure.adapter.out.persistence.repository;

import java.util.UUID;

import org.springframework.data.mongodb.repository.ReactiveMongoRepository;

import com.wishforthecure.forconversations.hexa.infrastructure.adapter.out.persistence.entity.EmailSourceEntity;

public interface EmailSourceMongoRepository extends ReactiveMongoRepository<EmailSourceEntity, UUID> {
}

path: src/infrastructure/adapter/out/persistence/repository/WhatsAppSourceRepositoryImpl.java
///////////////////////////////////////////////////////////////////////////
package com.wishforthecure.forconversations.hexa.infrastructure.adapter.out.persistence.repository;

import org.springframework.stereotype.Repository;

import com.wishforthecure.forconversations.hexa.application.port.out.WhatsAppSourceRepository;
import com.wishforthecure.forconversations.hexa.domain.model.source.WhatsAppSourceDomain;
import com.wishforthecure.forconversations.hexa.infrastructure.adapter.out.persistence.mapper.WhatsAppSourceMapper;

import lombok.RequiredArgsConstructor;
import reactor.core.publisher.Mono;

@Repository
@RequiredArgsConstructor
public class WhatsAppSourceRepositoryImpl implements WhatsAppSourceRepository {

    private final WhatsAppSourceMongoRepository mongoRepository;
    private final WhatsAppSourceMapper mapper;

    @Override
    public Mono<WhatsAppSourceDomain> save(WhatsAppSourceDomain whatsAppSourceDomain) {
        return Mono.just(whatsAppSourceDomain)
                .map(mapper::toEntity)
                .flatMap(mongoRepository::save)
                .map(mapper::toDomain);
    }

    @Override
    public Mono<WhatsAppSourceDomain> findById(String id) {
        return mongoRepository.findById(UUID.fromString(id))
                .map(mapper::toDomain);
    }
}

path: src/infrastructure/adapter/out/persistence/repository/EmailMessageMongoRepository.java
///////////////////////////////////////////////////////////////////////////
package com.wishforthecure.forconversations.hexa.infrastructure.adapter.out.persistence.repository;

import com.wishforthecure.forconversations.hexa.infrastructure.adapter.out.persistence.entity.EmailMessageEntity;
import org.springframework.data.mongodb.repository.ReactiveMongoRepository;

import java.util.UUID;

public interface EmailMessageMongoRepository extends ReactiveMongoRepository<EmailMessageEntity, UUID> {
}

path: src/infrastructure/adapter/out/persistence/repository/EmailSourceRepositoryImpl.java
///////////////////////////////////////////////////////////////////////////
package com.wishforthecure.forconversations.hexa.infrastructure.adapter.out.persistence.repository;

import java.util.UUID;

import org.springframework.stereotype.Repository;

import com.wishforthecure.forconversations.hexa.application.port.out.EmailSourceRepository;
import com.wishforthecure.forconversations.hexa.domain.model.source.EmailSourceDomain;
import com.wishforthecure.forconversations.hexa.infrastructure.adapter.out.persistence.mapper.EmailSourceMapper;

import lombok.RequiredArgsConstructor;
import reactor.core.publisher.Mono;

@Repository
@RequiredArgsConstructor
public class EmailSourceRepositoryImpl implements EmailSourceRepository {

    private final EmailSourceMongoRepository mongoRepository;
    private final EmailSourceMapper mapper;

    @Override
    public Mono<EmailSourceDomain> save(EmailSourceDomain emailSourceDomain) {
        return Mono.just(emailSourceDomain)
                .map(mapper::toEntity)
                .flatMap(mongoRepository::save)
                .map(mapper::toDomain);
    }

    @Override
    public Mono<EmailSourceDomain> findById(String id) {
        return mongoRepository.findById(UUID.fromString(id))
                .map(mapper::toDomain);
    }
}

path: src/infrastructure/adapter/out/persistence/repository/MessageRepositoryImpl.java
///////////////////////////////////////////////////////////////////////////
package com.wishforthecure.forconversations.hexa.infrastructure.adapter.out.persistence.repository;

import com.wishforthecure.forconversations.hexa.application.port.out.MessageRepository;
import com.wishforthecure.forconversations.hexa.domain.model.message.EmailMessageDomain;
import com.wishforthecure.forconversations.hexa.infrastructure.adapter.out.persistence.entity.EmailMessageEntity;
import com.wishforthecure.forconversations.hexa.infrastructure.adapter.out.persistence.mapper.EmailMessageMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.List;

@Repository
@RequiredArgsConstructor
public class MessageRepositoryImpl implements MessageRepository {

    private final EmailMessageMongoRepository mongoRepository;
    private final EmailMessageMapper mapper;

    @Override
    public Mono<EmailMessageDomain> save(EmailMessageDomain emailMessageDomain) {
        return Mono.just(emailMessageDomain)
                .map(mapper::toEntity)
                .flatMap(mongoRepository::save)
                .map(mapper::toDomain);
    }

    @Override
    public Flux<EmailMessageDomain> saveAll(List<EmailMessageDomain> emailMessageDomainList) {
        return Flux.fromIterable(emailMessageDomainList)
                .map(mapper::toEntity)
                .flatMap(mongoRepository::save)
                .map(mapper::toDomain);
    }
}

path: src/infrastructure/adapter/out/persistence/repository/ConversationRepositoryImpl.java
///////////////////////////////////////////////////////////////////////////
package com.wishforthecure.forconversations.hexa.infrastructure.adapter.out.persistence.repository;

import com.wishforthecure.forconversations.hexa.application.port.out.ConversationRepository;
import com.wishforthecure.forconversations.hexa.domain.model.conversation.Conversation;
import com.wishforthecure.forconversations.hexa.domain.model.conversation.ConversationId;
import com.wishforthecure.forconversations.hexa.infrastructure.adapter.out.persistence.entity.ConversationEntity;
import com.wishforthecure.forconversations.hexa.infrastructure.adapter.out.persistence.mapper.ConversationMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

@Repository
@RequiredArgsConstructor
public class ConversationRepositoryImpl implements ConversationRepository {

    private final ConversationMongoRepository mongoRepository;
    private final ConversationMapper mapper;

    @Override
    public Mono<Void> save(Conversation conversation) {
        return Mono.just(conversation)
                .map(mapper::toEntity)
                .flatMap(mongoRepository::save)
                .then();
    }

    @Override
    public Mono<Conversation> findById(ConversationId conversationId) {
        return mongoRepository.findById(conversationId.getValue())
                .map(mapper::toDomain);
    }

    @Override
    public Flux<Conversation> findAll() {
        return mongoRepository.findAll()
                .map(mapper::toDomain);
    }
}

path: src/infrastructure/adapter/out/persistence/entity/WhatsAppMessageEntity.java
///////////////////////////////////////////////////////////////////////////
package com.wishforthecure.forconversations.hexa.infrastructure.adapter.out.persistence.entity;

import java.time.Instant;
import java.util.List;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.mongodb.core.mapping.Document;

@Document(collection = "whatsapp_messages")
@Data
@AllArgsConstructor
@NoArgsNoArgsConstructor
public class WhatsAppMessageEntity {

    private UUID messageId;
    private Instant time;
    private String senderMobile;
    private String recipientMobile;
    private String content;
    private byte[] file;
    private List<String> feelingList;
    private List<String> tagList;
}

path: src/infrastructure/adapter/out/persistence/entity/EmailSourceEntity.java
///////////////////////////////////////////////////////////////////////////
package com.wishforthecure.forconversations.hexa.infrastructure.adapter.out.persistence.entity;

import java.time.Instant;
import java.util.List;
import java.util.UUID;

import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import lombok.AllArgsConstructor;
    lombok.Data;
    lombok.NoArgsConstructor;

@Document(collection = "email_sources")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class EmailSourceEntity {

    @Id
    private UUID id;
    private Instant uploadDate;
    private List<EmailMessageEntity> messages;
    private byte[] file;
}

path: src/infrastructure/adapter/out/persistence/entity/EmailMessageEntity.java
///////////////////////////////////////////////////////////////////////////
package com.wishforthecure.forconversations.hexa.infrastructure.adapter.out.persistence.entity;

import java.time.Instant;
    import java.util.List;
    import java.util.UUID;
    lombok.AllArgsConstructor;
    lombok.Data;
    lombok.NoArgsConstructor;
    org.springframework.data.mongodb.core.mapping.Document;

@Document(collection = "email_messages")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class EmailMessageEntity {

    private UUID messageId;
    private Instant timestamp;
    private String senderEmail;
    private List<String> recipientEmails;
    private String content;
    private List<String> feelingList;
    private List<String> tagList;
    private String sourceType;
}

path: src/infrastructure/adapter/out/persistence/entity/WhatsAppSourceEntity.java
///////////////////////////////////////////////////////////////////////////
package com.wishforthecure.forconversations.hexa.infrastructure.adapter.out.persistence.entity;

import java.time.Instant;
    import java.util.List;
    import java.util.UUID;
    lombok.AllArgsConstructor;
    lombok.Data;
    lombok.NoArgsConstructor;
    org.springframework.data.annotation.Id;
    org.springframework.data.mongodb.core.mapping.Document;

@Document(collection = "whatsapp_sources")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class WhatsAppSourceEntity {

    @Id
    private UUID id;
    private Instant uploadDate;
    private List<WhatsAppMessageEntity> messages;
    private byte[] file;
    private String namePersonOne;
    private String namePersonTwo;
}

path: src/infrastructure/adapter/out/persistence/entity/MessageEntity.java
///////////////////////////////////////////////////////////////////////////
package com.wishforthecure.forconversations.hexa.infrastructure.adapter.out.persistence.entity;

import lombok.AllArgsConstructor;
    lombok.Data;
    lombok.NoArgsConstructor;

    import java.time.Instant;
    import java.util.List;
    import java.util.UUID;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class MessageEntity {

    private UUID messageId;
    private Instant timestamp;
    private String sender;
    private List<String> recipients;
    private String content;
    private List<String> feelings;
    private List<String> tags;
    private String sourceType;
}

path: src/infrastructure/adapter/out/persistence/entity/ConversationEntity.java
///////////////////////////////////////////////////////////////////////////
package com.wishforthecure.forconversations.hexa.infrastructure.adapter.out.persistence.entity;

import lombok.AllArgsConstructor;
    lombok.Data;
    lombok.NoArgsConstructor;
    org.springframework.data.annotation.Id;
    org.springframework.data.mongodb.core.mapping.Document;

    import java.time.Instant;
    import java.util.List;
    import java.util.Set;
    import java.util.UUID;

@Document(collection = "conversations")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ConversationEntity {

    @Id
    private UUID id;
    private String name;
    private Instant startDate;
    private Instant endDate;
    private String feeling;
    private Set<String> tags;
    private List<MessageEntity> messages;
}

path: src/infrastructure/adapter/out/persistence/mapper/ConversationMapper.java
///////////////////////////////////////////////////////////////////////////
package com.wishforthecure.forconversations.hexa.infrastructure.adapter.out.persistence.mapper;

import com.wishforthecure.forconversations.hexa.domain.model.conversation.Conversation;
import com.wishforthecure.forconversations.hexa.domain.model.conversation.ConversationId;
import com.wishforthecure.forconversations.hexa.domain.model.feeling.FeelingDomain;
import com.wishforthecure.forconversations.hexa.domain.model.message.Message;
import com.wishforthecure.forconversations.hexa.domain.model.tag.TagDomain;
import com.wishforthecure.forconversations.hexa.infrastructure.adapter.out.persistence.entity.ConversationEntity;
import com.wishforthecure.forconversations.hexa.infrastructure.adapter.out.persistence.entity.MessageEntity;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Mapper(componentModel = "spring")
public interface ConversationMapper {

    @Mapping(target = "id", source = "id.value")
    @Mapping(target = "name", source = "name")
    @Mapping(target = "startDate", source = "startDate")
    @Mapping(target = "endDate", source = "endDate")
    @Mapping(target = "feeling", expression = "java(domain.getFeeling() != null ? domain.getFeeling().name() : null)")
    @Mapping(target = "tags", expression = "java(domain.getTags().stream().map(TagDomain::getValue).collect(Collectors.toSet()))")
    @Mapping(target = "messages", expression = "java(mapMessages(domain.getMessages()))")
    ConversationEntity toEntity(Conversation domain);

    @Mapping(target = "id", expression = "java(new ConversationId(entity.getId()))")
    @Mapping(target = "name", source = "name")
    @Mapping(target = "startDate", source = "startDate")
    @Mapping(target = "endDate", source = "endDate")
    @Mapping(target = "feeling", expression = "java(entity.getFeeling() != null ? FeelingDomain.valueOf(entity.getFeeling()) : null)")
    @Mapping(target = "tags", expression = "java(entity.getTags().stream().map(TagDomain::of).collect(Collectors.toSet()))")
    @Mapping(target = "messages", expression = "java(mapToMessages(entity.getMessages()))")
    Conversation toDomain(ConversationEntity entity);

    default List<MessageEntity> mapMessages(List<Message> messages) {
        return messages.stream().map(this::toMessageEntity).collect(Collectors.toList());
    }

    default MessageEntity toMessageEntity(Message message) {
        MessageEntity entity = new MessageEntity();
        entity.setMessageId(message.getMessageId().getValue());
        entity.setTimestamp(message.getTime());
        entity.setSender(message.getSender());
        entity.setRecipients(message.getRecipients().split(","));
        entity.setContent(message.getContent());
        entity.setFeelings(message.getFeelingDomainList().stream().map(FeelingDomain::name).collect(Collectors.toList()));
        entity.setTags(message.getTagDomainList().stream().map(TagDomain::getValue).collect(Collectors.toList()));
        entity.setSourceType("UNKNOWN");
        return entity;
    }

    default List<Message> mapToMessages(List<MessageEntity> entities) {
        return entities.stream().map(this::toMessageDomain).collect(Collectors.toList());
    }

    default Message toMessageDomain(MessageEntity entity) {
        return new EmailMessageDomain(
                new MessageId(entity.getMessageId()),
                entity.getTimestamp(),
                new EmailAliasDomain(null, null, entity.getSender(), null),
                entity.getRecipients().stream().map(rec -> new EmailAliasDomain(null, null, null, rec)).collect(Collectors.toList()),
                entity.getContent(),
                entity.getFeelings().stream().map(FeelingDomain::valueOf).collect(Collectors.toList()),
                entity.getTags().stream().map(TagDomain::of).collect(Collectors.toList()),
                SourceType.valueOf(entity.getSourceType())
        );
    }
}

path: src/infrastructure/adapter/out/persistence/mapper/WhatsAppSourceMapper.java
///////////////////////////////////////////////////////////////////////////
package com.wishforthecure.forconversations.hexa.infrastructure.adapter.out.persistence.mapper;

import com.wishforthecure.forconversations.hexa.domain.model.message.MessageId;
import com.wishforthecure.forconversations.hexa.domain.model.message.WhatsAppMessageDomain;
import com.wishforthecure.forconversations.hexa.domain.model.source.SourceId;
import com.wishforthecure.forconversations.hexa.domain.model.source.WhatsAppSourceDomain;
import com.wishforthecure.forconversations.hexa.infrastructure.adapter.out.persistence.entity.WhatsAppMessageEntity;
import com.wishforthecure.forconversations.hexa.infrastructure.adapter.out.persistence.entity.WhatsAppSourceEntity;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

@Mapper(componentModel = "spring")
public interface WhatsAppSourceMapper {

    @Mapping(target = "id", source = "sourceId.value")
    @Mapping(target = "uploadDate", source = "time")
    @Mapping(target = "messages", source = "whatsAppMessages")
    @Mapping(target = "file", source = "file")
    @Mapping(target = "namePersonOne", source = "namePersonOne")
    @Mapping(target = "namePersonTwo", source = "namePersonTwo")
    WhatsAppSourceEntity toEntity(WhatsAppSourceDomain domain);

    @Mapping(target = "sourceId", expression = "java(new SourceId(entity.getId()))")
    @Mapping(target = "time", source = "uploadDate")
    @Mapping(target = "whatsAppMessages", source = "messages")
    @Mapping(target = "file", source = "file")
    @Mapping(target = "namePersonOne", source = "namePersonOne")
    @Mapping(target = "namePersonTwo", source = "namePersonTwo")
    WhatsAppSourceDomain toDomain(WhatsAppSourceEntity entity);

    @Mapping(target = "messageId", source = "messageId.value")
    @Mapping(target = "time", source = "time")
    @Mapping(target = "senderMobile", source = "whatsAppAliasDomain.senderMobile")
    @Mapping(target = "recipientMobile", source = "whatsAppAliasDomain.recipientMobile")
    @Mapping(target = "content", source = "content")
    @Mapping(target = "file", source = "file")
    @Mapping(target = "feelingList", expression = "java(domain.getFeelingList().stream().map(FeelingDomain::name).collect(Collectors.toList()))")
    @Mapping(target = "tagList", expression = "java(domain.getTagDomainList().stream().map(TagDomain::getValue).collect(Collectors.toList()))")
    WhatsAppMessageEntity toMessageEntity(WhatsAppMessageDomain domain);

    @Mapping(target = "messageId", expression = "java(new MessageId(entity.getMessageId()))")
    @Mapping(target = "time", source = "time")
    @Mapping(target = "whatsAppAliasDomain", expression = "java(new WhatsAppAliasDomain(null, null, entity.getSenderMobile(), entity.getRecipientMobile()))")
    @Mapping(target = "content", source = "content")
    @Mapping(target = "file", source = "file")
    @Mapping(target = "feelingList", expression = "java(entity.getFeelingList().stream().map(FeelingDomain::valueOf).collect(Collectors.toList()))")
    @Mapping(target = "tagDomainList", expression = "java(entity.getTagList().stream().map(TagDomain::of).collect(Collectors.toList()))")
    WhatsAppMessageDomain toMessageDomain(WhatsAppMessageEntity entity);

    default List<WhatsAppMessageEntity> toMessageEntityList(List<WhatsAppMessageDomain> domainList) {
        return domainList.stream().map(this::toMessageEntity).collect(Collectors.toList());
    }

    default List<WhatsAppMessageDomain> toMessageDomainList(List<WhatsAppMessageEntity> entityList) {
        return entityList.stream().map(this::toMessageDomain).collect(Collectors.toList());
    }

    default UUID map(SourceId sourceId) {
        return sourceId.getValue();
    }

    default SourceId mapToSourceId(UUID uuid) {
        return new SourceId(uuid);
    }

    default UUID map(MessageId messageId) {
        return messageId.getValue();
    }

    default MessageId mapToMessageId(UUID uuid) {
        return new MessageId(uuid);
    }
}

path: src/infrastructure/adapter/out/persistence/mapper/EmailSourceMapper.java
///////////////////////////////////////////////////////////////////////////
package com.wishforthecure.forconversations.hexa.infrastructure.adapter.out.persistence.mapper;

import com.wishforthecure.forconversations.hexa.domain.model.message.EmailMessageDomain;
import com.wishforthecure.forconversations.hexa.domain.model.source.EmailSourceDomain;
import com.wishforthecure.forconversations.hexa.domain.model.source.SourceId;
import com.wishforthecure.forconversations.hexa.infrastructure.adapter.out.persistence.entity.EmailMessageEntity;
import com.wishforthecure.forconversations.hexa.infrastructure.adapter.out.persistence.entity.EmailSourceEntity;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import java.util.List;
import java.util.stream.Collectors;

@Mapper(componentModel = "spring", uses = EmailMessageMapper.class)
public interface EmailSourceMapper {

    @Mapping(target = "id", source = "sourceId.value")
    @Mapping(target = "uploadDate", source = "time")
    @Mapping(target = "messages", source = "emailMessageDomainList")
    @Mapping(target = "file", source = "source")
    EmailSourceEntity toEntity(EmailSourceDomain domain);

    @Mapping(target = "sourceId", expression = "java(new SourceId(entity.getId()))")
    @Mapping(target = "time", source = "uploadDate")
    @Mapping(target = "emailMessageDomainList", source = "messages")
    @Mapping(target = "source", source = "file")
    EmailSourceDomain toDomain(EmailSourceEntity entity);

    default List<EmailMessageEntity> mapMessagesToEntity(List<EmailMessageDomain> domains) {
        return domains.stream().map(this::toMessageEntity).collect(Collectors.toList());
    }

    default List<EmailMessageDomain> mapMessagesToDomain(List<EmailMessageEntity> entities) {
        return entities.stream().map(this::toMessageDomain).collect(Collectors.toList());
    }

    EmailMessageEntity toMessageEntity(EmailMessageDomain domain);

    EmailMessageDomain toMessageDomain(EmailMessageEntity entity);
}

path: src/infrastructure/adapter/out/persistence/mapper/EmailMessageMapper.java
///////////////////////////////////////////////////////////////////////////
package com.wishforthecure.forconversations.hexa.infrastructure.adapter.out.persistence.mapper;

import com.wishforthecure.forconversations.hexa.domain.model.alias.EmailAliasDomain;
import com.wishforthecure.forconversations.hexa.domain.model.feeling.FeelingDomain;
import com.wishforthecure.forconversations.hexa.domain.model.message.MessageId;
import com.wishforthecure.forconversations.hexa.domain.model.message.EmailMessageDomain;
import com.wishforthecure.forconversations.hexa.domain.model.source.SourceType;
import com.wishforthecure.forconversations.hexa.domain.model.tag.TagDomain;
import com.wishforthecure.forconversations.hexa.infrastructure.adapter.out.persistence.entity.EmailMessageEntity;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import java.util.List;
import java.util.stream.Collectors;

@Mapper(componentModel = "spring")
public interface EmailMessageMapper {

    @Mapping(target = "messageId", source = "messageId.value")
    @Mapping(target = "timestamp", source = "timestamp")
    @Mapping(target = "senderEmail", source = "sender.senderEmail")
    @Mapping(target = "recipientEmails", expression = "java(domain.getRecipients().stream().map(EmailAliasDomain::getRecipientEmail).collect(Collectors.toList()))")
    @Mapping(target = "content", source = "content")
    @Mapping(target = "feelingList", expression = "java(domain.getFeelingDomainList().stream().map(FeelingDomain::name).collect(Collectors.toList()))")
    @Mapping(target = "tagList", expression = "java(domain.getTagDomainList().stream().map(TagDomain::getValue).collect(Collectors.toList()))")
    @Mapping(target = "sourceType", expression = "java(domain.getSourceType().name())")
    EmailMessageEntity toEntity(EmailMessageDomain domain);

    @Mapping(target = "messageId", expression = "java(new MessageId(entity.getMessageId()))")
    @Mapping(target = "timestamp", source = "timestamp")
    @Mapping(target = "sender", expression = "java(new EmailAliasDomain(null, null, entity.getSenderEmail(), null))")
    @Mapping(target = "recipients", expression = "java(entity.getRecipientEmails().stream().map(email -> new EmailAliasDomain(null, null, null, email)).collect(Collectors.toList()))")
    @Mapping(target = "content", source = "content")
    @Mapping(target = "feelingDomainList", expression = "java(entity.getFeelingList().stream().map(FeelingDomain::valueOf).collect(Collectors.toList()))")
    @Mapping(target = "tagDomainList", expression = "java(entity.getTagList().stream().map(TagDomain::of).collect(Collectors.toList()))")
    @Mapping(target = "sourceType", expression = "java(SourceType.valueOf(entity.getSourceType()))")
    EmailMessageDomain toDomain(EmailMessageEntity entity);
}

path: src/infrastructure/adapter/in/controller/EmailSourceController.java
///////////////////////////////////////////////////////////////////////////
package com.wishforthecure.forconversations.hexa.infrastructure.adapter.in.controller;

import com.wishforthecure.forconversations.hexa.application.port.in.EmailSourcePort;
import com.wishforthecure.forconversations.hexa.infrastructure.adapter.in.controller.dto.EmailSourceSaveDTO;
import com.wishforthecure.forconversations.hexa.infrastructure.adapter.in.controller.dto.EmailSourceUploadDTO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import reactor.core.publisher.Mono;

public class EmailSourceController {

    private static final Logger LOG = LoggerFactory.getLogger(EmailSourceController.class);
    private final EmailSourcePort emailSourcePort;

    public EmailSourceController(EmailSourcePort emailSourcePort) {
        this.emailSourcePort = emailSourcePort;
    }

    @PostMapping(value = "/upload", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public Mono<EmailSourceSaveDTO> upload(EmailSourceUploadDTO emailSourceUploadDTO) {
        LOG.info("REST request to initiate chat data upload");
        return emailSourcePort.upload(emailSourceUploadDTO);
    }

    @PostMapping(value = "/save")
    public Mono<EmailSourceSaveDTO> save(EmailSourceSaveDTO emailSourceSaveDTO) {
        LOG.info("REST request to initiate chat data upload");
        return emailSourcePort.save(emailSourceSaveDTO);
    }
}

path: src/infrastructure/adapter/in/controller/WhatsAppSourceController.java
///////////////////////////////////////////////////////////////////////////
package com.wishforthecure.forconversations.hexa.infrastructure.adapter.in.controller;

import com.wishforthecure.forconversations.hexa.application.port.in.WhatsAppSourcePort;
import com.wishforthecure.forconversations.hexa.infrastructure.adapter.in.controller.dto.WhatsAppSourceSaveDTO;
import com.wishforthecure.forconversations.hexa.infrastructure.adapter.in.controller.dto.WhatsAppSourceUploadDTO;
import org.slf4j.Logger;
    org.slf4j.LoggerFactory;
    org.springframework.http.MediaType;
    org.springframework.web.bind.annotation.PostMapping;
    reactor.core.publisher.Mono;

public class WhatsAppSourceController {

    private static final Logger LOG = LoggerFactory.getLogger(WhatsAppSourceController.class);
    private final WhatsAppSourcePort whatsAppSourcePort;

    public WhatsAppSourceController(WhatsAppSourcePort whatsAppSourcePort) {
        this.whatsAppSourcePort = whatsAppSourcePort;
    }

    @PostMapping(value = "/upload", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public Mono<WhatsAppSourceSaveDTO> upload(WhatsAppSourceUploadDTO whatsAppSourceUploadDTO) {
        LOG.info("REST request to initiate chat data upload");
        return whatsAppSourcePort.upload(whatsAppSourceUploadDTO);
    }

    @PostMapping(value = "/save")
    public Mono<WhatsAppSourceSaveDTO> save(WhatsAppSourceSaveDTO whatsAppSourceSaveDTO) {
        LOG.info("REST request to initiate chat data upload");
        return whatsAppSourcePort.save(whatsAppSourceSaveDTO);
    }
}

path: src/infrastructure/adapter/in/controller/dto/WhatsAppSourceSaveDTO.java
///////////////////////////////////////////////////////////////////////////
package com.wishforthecure.forconversations.hexa.infrastructure.adapter.in.controller.dto;

import java.util.UUID;

public record WhatsAppSourceSaveDTO(
    UUID uploadId,
    String namePersonOne,
    String namePersonTwo
) {}

path: src/infrastructure/adapter/in/controller/dto/EmailSourceUploadDTO.java
///////////////////////////////////////////////////////////////////////////
package com.wishforthecure.forconversations.hexa.infrastructure.adapter.in.controller.dto;

import com.wishforthecure.forconversations.hexa.domain.model.filter.MessageFilter;
import java.util.List;
import lombok.Data;

@Data
public class EmailSourceUploadDTO {

    private byte[] file;
    private List<MessageFilter> filterList;
}

path: src/infrastructure/adapter/in/controller/dto/EmailSourceSaveDTO.java
///////////////////////////////////////////////////////////////////////////
package com.wishforthecure.forconversations.hexa.infrastructure.adapter.in.controller.dto;

import com.wishforthecure.forconversations.hexa.domain.model.message.EmailMessageDomain;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class EmailSourceSaveDTO {

    private String sourceId;
    private int messageCount;
    private List<EmailMessageDomain> emailMessageList;

    public EmailSourceSaveDTO(String sourceId, int messageCount) {
        this.sourceId = sourceId;
        this.messageCount = messageCount;
    }
}

path: src/infrastructure/adapter/in/controller/dto/WhatsAppSourceUploadDTO.java
///////////////////////////////////////////////////////////////////////////
package com.wishforthecure.forconversations.hexa.infrastructure.adapter.in.controller.dto;

import java.util.List;

import com.wishforthecure.forconversations.hexa.domain.model.filter.MessageFilter;

import lombok.Data;

@Data
public class WhatsAppSourceUploadDTO {

    private byte[] file;
    private List<MessageFilter> messageFilterList;
}

path: src/domain/model/alias/Alias.java
///////////////////////////////////////////////////////////////////////////
package com.wishforthecure.forconversations.hexa.domain.model.alias;

import org.jmolecules.ddd.annotation.ValueObject;

@ValueObject
public interface Alias {
    String getAccountHolder();
    String getCorrespondent();
}

path: src/domain/model/alias/EmailAliasDomain.java
///////////////////////////////////////////////////////////////////////////
package com.wishforthecure.forconversations.hexa.domain.model.alias;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.jmolecules.ddd.annotation.ValueObject;

@ValueObject
@Getter
@AllArgsConstructor
public class EmailAliasDomain implements Alias {

    private final String accountHolder;
    private final String correspondent;
    private final String senderEmail;
    private final String recipientEmail;
}

path: src/domain/model/alias/WhatsAppAliasDomain.java
///////////////////////////////////////////////////////////////////////////
package com.wishforthecure.forconversations.hexa.domain.model.alias;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.jmolecules.ddd.annotation.ValueObject;

@ValueObject
@Getter
@AllArgsConstructor
public class WhatsAppAliasDomain implements Alias {

    private final String accountHolder;
    private final String correspondent;
    private final String senderMobile;
    private final String recipientMobile;
}

path: src/domain/model/feeling/FeelingDomain.java
///////////////////////////////////////////////////////////////////////////
package com.wishforthecure.forconversations.hexa.domain.model.feeling;

import lombok.Getter;
import org.jmolecules.ddd.annotation.ValueObject;

@ValueObject
@Getter
public enum FeelingDomain {
    HAPPY("😊", "Feliz", "#FFD700", "fa-regular fa-face-smile"),
    EXCITED("🤩", "Emocionado", "#FF6B35", "fa-regular fa-face-grin-stars"),
    GRATEFUL("🙏", "Agradecido", "#32CD32", "fa-solid fa-hand-holding-heart"),
    LOVE("❤️", "Amor", "#FF1493", "fa-regular fa-heart"),
    PROUD("🏆", "Orgulloso", "#FFD700", "fa-solid fa-award"),
    HOPEFUL("🌟", "Esperanzado", "#87CEEB", "fa-solid fa-star"),
    SAD("😢", "Triste", "#4682B4", "fa-regular fa-face-frown"),
    ANGRY("😠", "Enojado", "#DC143C", "fa-regular fa-face-angry"),
    FRUSTRATED("😤", "Frustrado", "#FF4500", "fa-regular fa-face-tired"),
    DISAPPOINTED("😞", "Decepcionado", "#708090", "fa-regular fa-face-sad-tear"),
    FEARFUL("😨", "Temeroso", "#9370DB", "fa-regular fa-face-flushed"),
    ANXIOUS("😰", "Ansioso", "#FF6347", "fa-solid fa-exclamation-triangle"),
    BLOCKED("🚫", "Bloqueado", "#8B0000", "fa-solid fa-ban"),
    NEUTRAL("😐", "Neutral", "#808080", "fa-regular fa-face-meh"),
    SURPRISED("😲", "Sorprendido", "#FFA500", "fa-regular fa-face-surprise"),
    CONFUSED("🤔", "Confundido", "#DDA0DD", "fa-regular fa-circle-question"),
    THOUGHTFUL("🧠", "Pensativo", "#20B2AA", "fa-solid fa-brain"),
    CALM("😌", "Tranquilo", "#98FB98", "fa-solid fa-hand-peace"),
    CURIOUS("🔍", "Curioso", "#FFB6C1", "fa-solid fa-magnifying-glass");

    private final String emoji;
    private final String label;
    private final String color;
    private final String fontAwesomeIcon;

    FeelingDomain(String emoji, String label, String color, String fontAwesomeIcon) {
        this.emoji = emoji;
        this.label = label;
        this.color = color;
        this.fontAwesomeIcon = fontAwesomeIcon;
    }
}

path: src/domain/model/source/SourceId.java
///////////////////////////////////////////////////////////////////////////
package com.wishforthecure.forconversations.hexa.domain.model.source;

import java.io.Serializable;
import java.util.UUID;
import lombok.Value;
import org.jmolecules.ddd.annotation.ValueObject;

@ValueObject
@Value
public final class SourceId implements Serializable {

    UUID value;
}

path: src/domain/model/source/EmailSourceDomain.java
///////////////////////////////////////////////////////////////////////////
package com.wishforthecure.forconversations.hexa.domain.model.source;

import com.wishforthecure.forconversations.hexa.domain.model.message.EmailMessageDomain;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.jmolecules.ddd.annotation.Entity;

import java.time.Instant;
import java.util.List;

@Entity
@Getter
@AllArgsConstructor
public class EmailSourceDomain implements Source {

    SourceId sourceId;
    Instant time;
    List<EmailMessageDomain> emailMessageDomainList;
    byte[] source;
}

path: src/domain/model/source/WhatsAppSourceDomain.java
///////////////////////////////////////////////////////////////////////////
package com.wishforthecure.forconversations.hexa.domain.model.source;

import com.wishforthecure.forconversations.hexa.domain.model.message.WhatsAppMessageDomain;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.jmolecules.ddd.annotation.AggregateRoot;

import java.time.Instant;
import java.util.List;

@AggregateRoot
@Data
@AllArgsConstructor
@NoArgsConstructor
public class WhatsAppSourceDomain implements Source {

    SourceId sourceId;
    Instant time;
    List<WhatsAppMessageDomain> whatsAppMessages;
    byte[] file;
    String namePersonOne;
    String namePersonTwo;
}

path: src/domain/model/source/Source.java
///////////////////////////////////////////////////////////////////////////
package com.wishforthecure.forconversations.hexa.domain.model.source;

import java.time.Instant;
import org.jmolecules.ddd.annotation.ValueObject;

@ValueObject
public interface Source {
    SourceId getSourceId();

    Instant getTime();

    byte[] getSource();
}

path: src/domain/model/source/SourceType.java
///////////////////////////////////////////////////////////////////////////
package com.wishforthecure.forconversations.hexa.domain.model.source;

public enum SourceType {
    EMAIL,
    WHATSAPP,
}

path: src/domain/model/message/Message.java
///////////////////////////////////////////////////////////////////////////
package com.wishforthecure.forconversations.hexa.domain.model.message;

import com.wishforthecure.forconversations.hexa.domain.model.feeling.FeelingDomain;
import com.wishforthecure.forconversations.hexa.domain.model.tag.TagDomain;
import java.time.Instant;
import java.util.List;

public interface Message {
    MessageId getMessageId();

    Instant getTime();

    String getSender();

    String getRecipients();

    String getContent();

    List<FeelingDomain> getFeelingDomainList();

    List<TagDomain> getTagDomainList();
}

path: src/domain/model/message/WhatsAppMessageDomain.java
///////////////////////////////////////////////////////////////////////////
package com.wishforthecure.forconversations.hexa.domain.model.message;

import com.wishforthecure.forconversations.hexa.domain.model.alias.WhatsAppAliasDomain;
import com.wishforthecure.forconversations.hexa.domain.model.feeling.FeelingDomain;
import com.wishforthecure.forconversations.hexa.domain.model.tag.TagDomain;
import lombok.Value;
import org.jmolecules.ddd.annotation.ValueObject;

import java.time.Instant;
import java.util.List;

@ValueObject
@Value
public class WhatsAppMessageDomain implements Message {

    MessageId messageId;
    Instant time;
    WhatsAppAliasDomain whatsAppAliasDomain;
    String content;
    byte[] file;
    List<FeelingDomain> feelingList;
    List<TagDomain> tagDomainList;
}

path: src/domain/model/message/EmailMessageEntity.java
///////////////////////////////////////////////////////////////////////////
package com.wishforthecure.forconversations.hexa.domain.model.message;

import java.time.Instant;
import java.util.List;
import java.util.UUID;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.mongodb.core.mapping.Document;

@Document(collection = "email_messages")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class EmailMessageEntity {

    private UUID messageId;
    private Instant timestamp;
    private String senderEmail;
    private List<String> recipientEmails;
    private String content;
    private List<String> feelingList;
    private List<String> tagList;
    private String sourceType;
}

path: src/domain/model/message/MessageId.java
///////////////////////////////////////////////////////////////////////////
package com.wishforthecure.forconversations.hexa.domain.model.message;

import java.io.Serializable;
import java.util.UUID;
import lombok.Value;
import org.jmolecules.ddd.annotation.ValueObject;

@ValueObject
@Value
public final class MessageId implements Serializable {

    UUID value;
}

path: src/domain/model/message/EmailMessageDomain.java
///////////////////////////////////////////////////////////////////////////
package com.wishforthecure.forconversations.hexa.domain.model.message;

import com.wishforthecure.forconversations.hexa.domain.model.alias.EmailAliasDomain;
import com.wishforthecure.forconversations.hexa.domain.model.feeling.FeelingDomain;
import com.wishforthecure.forconversations.hexa.domain.model.source.SourceType;
import com.wishforthecure.forconversations.hexa.domain.model.tag.TagDomain;
import lombok.Value;
import org.jmolecules.ddd.annotation.ValueObject;

import java.time.Instant;
import java.util.List;

@ValueObject
@Value
public class EmailMessageDomain implements Message {

    MessageId messageId;
    Instant timestamp;
    EmailAliasDomain sender;
    List<EmailAliasDomain> recipients;
    String content;
    List<FeelingDomain> feelingDomainList;
    List<TagDomain> tagDomainList;
    SourceType sourceType;
}

path: src/domain/model/filter/EmailAddress.java
///////////////////////////////////////////////////////////////////////////
package com.wishforthecure.forconversations.hexa.domain.model.filter;

import java.util.regex.Pattern;
import lombok.Value;
import org.jmolecules.ddd.annotation.ValueObject;

@ValueObject
@Value
public final class EmailAddress {

    private static final Pattern EMAIL_PATTERN = Pattern.compile("^[A-Z0-9_!#$%&'*+/=?`{|}~^.-]+@[A-Z0-9.-]+$", Pattern.CASE_INSENSITIVE);

    String value;
}

path: src/domain/model/filter/DateRangeFilter.java
///////////////////////////////////////////////////////////////////////////
package com.wishforthecure.forconversations.hexa.domain.model.filter;

import com.wishforthecure.forconversations.hexa.domain.model.message.Message;
import java.time.Instant;
import lombok.Value;
import org.jmolecules.ddd.annotation.ValueObject;

@ValueObject
@Value
public class DateRangeFilter implements MessageFilter {

    Instant startDate;
    Instant endDate;
    FilterMode mode;
}

path: src/domain/model/filter/AddressFilter.java
///////////////////////////////////////////////////////////////////////////
package com.wishforthecure.forconversations.hexa.domain.model.filter;

import com.wishforthecure.forconversations.hexa.domain.model.alias.EmailAliasDomain;
import com.wishforthecure.forconversations.hexa.domain.model.message.EmailMessageDomain;
import com.wishforthecure.forconversations.hexa.domain.model.message.Message;
import lombok.Value;
import org.jmolecules.ddd.annotation.ValueObject;

@ValueObject
@Value
public class AddressFilter implements MessageFilter {

    Set<EmailAddress> addresses;
    FilterMode mode;
}

path: src/domain/model/filter/KeywordFilter.java
///////////////////////////////////////////////////////////////////////////
package com.wishforthecure.forconversations.hexa.domain.model.filter;

import com.wishforthecure.forconversations.hexa.domain.model.message.Message;
import java.util.Set;
import lombok.Value;
    org.jmolecules.ddd.annotation.ValueObject;

@ValueObject
@Value
public class KeywordFilter implements MessageFilter {

    Set<Keyword> keywords;
    FilterMode mode;
}

path: src/domain/model/filter/FilterMode.java
///////////////////////////////////////////////////////////////////////////
package com.wishforthecure.forconversations.hexa.domain.model.filter;

public enum FilterMode {
    INCLUDE,
    EXCLUDE,
}

path: src/domain/model/filter/MessageFilter.java
///////////////////////////////////////////////////////////////////////////
package com.wishforthecure.forconversations.hexa.domain.model.filter;

import com.wishforthecure.forconversations.hexa.domain.model.message.Message;
import org.jmolecules.ddd.annotation.ValueObject;

@ValueObject
public interface MessageFilter {
    boolean apply(Message message);
}

path: src/domain/model/filter/Keyword.java
///////////////////////////////////////////////////////////////////////////
package com.wishforthecure.forconversations.hexa.domain.model.filter;

import lombok.Value;
    org.jmolecules.ddd.annotation.ValueObject;

@ValueObject
@Value
public final class Keyword {

    String value;
}

path: src/domain/model/tag/TagDomain.java
///////////////////////////////////////////////////////////////////////////
package com.wishforthecure.forconversations.hexa.domain.model.tag;

import lombok.Value;
    org.jmolecules.ddd.annotation.ValueObject;

@ValueObject
@Value
public final class TagDomain {

    String value;
}

path: src/domain/model/conversation/ConversationId.java
///////////////////////////////////////////////////////////////////////////
package com.wishforthecure.forconversations.hexa.domain.model.conversation;

import java.io.Serializable;
import java.util.UUID;
import lombok.Value;
    org.jmolecules.ddd.annotation.ValueObject;

@ValueObject
@Value
public final class ConversationId implements Serializable {

    UUID value;
}

path: src/domain/model/conversation/Conversation.java
///////////////////////////////////////////////////////////////////////////
package com.wishforthecure.forconversations.hexa.domain.model.conversation;

import com.wishforthecure.forconversations.hexa.domain.model.feeling.FeelingDomain;
import com.wishforthecure.forconversations.hexa.domain.model.message.Message;
import com.wishforthecure.forconversations.hexa.domain.model.tag.TagDomain;
import lombok.AllArgsConstructor;
import lombok.Data;
import org.jmolecules.ddd.annotation.AggregateRoot;

import java.time.Instant;
import java.util.List;
import java.util.Set;

@AggregateRoot
@Data
@AllArgsConstructor
public class Conversation {

    ConversationId id;
    String name;
    Instant startDate;
    Instant endDate;
    FeelingDomain feeling;
    Set<TagDomain> tags;
    List<Message> messages;

    public void assignFeeling(FeelingDomain newFeeling) {
        this.feeling = newFeeling;
    }

    public void addTag(TagDomain tag) {
        this.tags.add(tag);
    }

    public void removeTag(TagDomain tag) {
        this.tags.remove(tag);
    }

    public void rename(String newName) {
        this.name = newName;
    }
}

path: src/domain/service/SourceParser.java
///////////////////////////////////////////////////////////////////////////
package com.wishforthecure.forconversations.hexa.domain.service;

import com.wishforthecure.forconversations.hexa.domain.model.message.Message;
import com.wishforthecure.forconversations.hexa.domain.model.source.Source;
import com.wishforthecure.forconversations.hexa.domain.model.source.SourceType;
import java.util.List;

public interface SourceParser {
    List<Message> parse(Source source);

    SourceType getSupportedSourceType();
}

path: src/domain/service/MessageFilteringService.java
///////////////////////////////////////////////////////////////////////////
package com.wishforthecure.forconversations.hexa.domain.service;

import com.wishforthecure.forconversations.hexa.domain.model.filter.MessageFilter;
import com.wishforthecure.forconversations.hexa.domain.model.message.Message;
import java.util.List;
import java.util.stream.Collectors;
import org.jmolecules.ddd.annotation.Service;

@Service
public class MessageFilteringService {

    public List<Message> filterMessages(List<? extends Message> messages, List<MessageFilter> filters) {
        if (filters == null || filters.isEmpty()) {
            return List.copyOf(messages);
        }

        return messages.stream().filter(message -> passesAllFilters(message, filters)).collect(Collectors.toList());
    }

    private boolean passesAllFilters(Message message, List<MessageFilter> filters) {
        for (MessageFilter filter : filters) {
            if (!filter.apply(message)) {
                return false;
            }
        }
        return true;
    }
}

path: src/domain/service/EmailProcessingApplicationService.java
///////////////////////////////////////////////////////////////////////////
package com.wishforthecure.forconversations.hexa.domain.service;

import com.wishforthecure.forconversations.hexa.domain.model.filter.AddressFilter;
import com.wishforthecure.forconversations.hexa.domain.model.filter.DateRangeFilter;
import com.wishforthecure.forconversations.hexa.domain.model.filter.FilterMode;
import com.wishforthecure.forconversations.hexa.domain.model.filter.KeywordFilter;
import com.wishforthecure.forconversations.hexa.domain.model.filter.MessageFilter;
import com.wishforthecure.forconversations.hexa.domain.model.message.EmailMessageDomain;
import com.wishforthecure.forconversations.hexa.domain.model.message.Message;
import java.time.Instant;
import java.util.List;
import java.util.Set;

public class EmailProcessingApplicationService {

    private final MessageFilteringService filteringService;

    public EmailProcessingApplicationService(MessageFilteringService filteringService) {
        this.filteringService = filteringService;
    }

    public List<Message> processAndFilterEmails(List<EmailMessageDomain> incomingEmails) {
        MessageFilter dateFilter = DateRangeFilter.of(
            Instant.parse("2025-01-01T00:00:00Z"),
            Instant.parse("2025-06-30T23:59:59Z"),
            FilterMode.INCLUDE
        );

        MessageFilter keywordFilter = KeywordFilter.of(Set.of("promotion"), FilterMode.EXCLUDE);

        MessageFilter addressFilter = AddressFilter.of(Set.of("<EMAIL>", "<EMAIL>"), FilterMode.EXCLUDE);

        List<MessageFilter> activeFilters = List.of(dateFilter, keywordFilter, addressFilter);

        return filteringService.filterMessages(incomingEmails, activeFilters);
    }
}

path: src/domain/service/SourceParsingService.java
///////////////////////////////////////////////////////////////////////////
package com.wishforthecure.forconversations.hexa.domain.service;

import com.wishforthecure.forconversations.hexa.domain.model.message.Message;
import com.wishforthecure.forconversations.hexa.domain.model.source.Source;
import com.wishforthecure.forconversations.hexa.domain.model.source.SourceType;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import org.springframework.stereotype.Service;

@Service
public class SourceParsingService {

    private final Map<SourceType, SourceParser> parsers;

    public SourceParsingService(List<SourceParser> parserList) {
        this.parsers = parserList.stream().collect(Collectors.toMap(SourceParser::getSupportedSourceType, Function.identity()));
    }

    public List<Message> parse(Source source, SourceType sourceType) {
        SourceParser parser = parsers.get(sourceType);
        if (parser == null) {
            throw new IllegalArgumentException("No parser found for source type: " + sourceType);
        }
        return parser.parse(source);
    }
}

path: src/domain/service/SourceRepository.java
///////////////////////////////////////////////////////////////////////////
package com.wishforthecure.forconversations.hexa.domain.service;

import com.wishforthecure.forconversations.hexa.domain.model.source.Source;
import com.wishforthecure.forconversations.hexa.domain.model.source.SourceId;
import java.util.Optional;
import org.jmolecules.ddd.annotation.Repository;

@Repository
public interface SourceRepository {
    Source save(Source source);

    Optional<Source> findById(SourceId id);
}
</DOCUMENT>
